// sw.js - Service Worker for caching MediaPipe models
const CACHE_NAME = "mediapipe-models-v1";
const MODEL_URLS = [
  "https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/latest/face_landmarker.task",
  // Add other MediaPipe WASM files as needed
];

// Install event - pre-cache the model
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches
      .open(CACHE_NAME)
      .then((cache) => {
        console.log("Pre-caching MediaPipe models...");
        return cache.addAll(MODEL_URLS);
      })
      .then(() => {
        console.log("MediaPipe models cached successfully");
        self.skipWaiting();
      })
      .catch((error) => {
        console.error("Failed to cache MediaPipe models:", error);
      })
  );
});

// Activate event
self.addEventListener("activate", (event) => {
  event.waitUntil(
    caches
      .keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        self.clients.claim();
      })
  );
});

// Fetch event - serve from cache first, then network
self.addEventListener("fetch", (event) => {
  // Only handle MediaPipe related requests
  if (
    event.request.url.includes("mediapipe") ||
    event.request.url.includes("storage.googleapis.com/mediapipe-models")
  ) {
    event.respondWith(
      caches.match(event.request).then((response) => {
        if (response) {
          console.log("Serving MediaPipe file from cache:", event.request.url);
          return response;
        }

        // If not in cache, fetch and cache
        return fetch(event.request).then((response) => {
          // Check if valid response
          if (
            !response ||
            response.status !== 200 ||
            response.type !== "basic"
          ) {
            return response;
          }

          const responseToCache = response.clone();
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseToCache);
            console.log("Cached MediaPipe file:", event.request.url);
          });

          return response;
        });
      })
    );
  }
});
