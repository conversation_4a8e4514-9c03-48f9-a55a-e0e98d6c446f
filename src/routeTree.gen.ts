/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated/route'
import { Route as AuthenticatedIndexImport } from './routes/_authenticated/index'
import { Route as errors503Import } from './routes/(errors)/503'
import { Route as errors500Import } from './routes/(errors)/500'
import { Route as errors404Import } from './routes/(errors)/404'
import { Route as errors403Import } from './routes/(errors)/403'
import { Route as errors401Import } from './routes/(errors)/401'
import { Route as authLoginImport } from './routes/(auth)/login'
import { Route as AuthenticatedAdminRouteImport } from './routes/_authenticated/admin/route'
import { Route as interviewInterviewIndexImport } from './routes/(interview)/interview/index'
import { Route as interviewInterviewOnboardingIndexImport } from './routes/(interview)/interview-onboarding/index'
import { Route as interviewInterviewOnboardingSystemSetupIndexImport } from './routes/(interview)/interview-onboarding-system-setup/index'
import { Route as interviewInterviewCompleteIndexImport } from './routes/(interview)/interview-complete/index'
import { Route as candidateJobListIndexImport } from './routes/(candidate)/job-list/index'
import { Route as AuthenticatedAdminSettingsRouteImport } from './routes/_authenticated/admin/settings/route'
import { Route as AuthenticatedAdminSettingsIndexImport } from './routes/_authenticated/admin/settings/index'
import { Route as AuthenticatedAdminProfileIndexImport } from './routes/_authenticated/admin/profile/index'
import { Route as AuthenticatedAdminJobsIndexImport } from './routes/_authenticated/admin/jobs/index'
import { Route as AuthenticatedAdminInterviewerPersonaIndexImport } from './routes/_authenticated/admin/interviewer-persona/index'
import { Route as AuthenticatedAdminDashboardIndexImport } from './routes/_authenticated/admin/dashboard/index'
import { Route as AuthenticatedAdminApplicantsIndexImport } from './routes/_authenticated/admin/applicants/index'
import { Route as candidateJobApplicationJobIdIndexImport } from './routes/(candidate)/job-application/$jobId/index'
import { Route as AuthenticatedAdminSettingsUserRoleImport } from './routes/_authenticated/admin/settings/user-role'
import { Route as AuthenticatedAdminSettingsUserManagementImport } from './routes/_authenticated/admin/settings/user-management'
import { Route as AuthenticatedAdminSettingsJobsImport } from './routes/_authenticated/admin/settings/jobs'
import { Route as AuthenticatedAdminJobsEditIndexImport } from './routes/_authenticated/admin/jobs/edit/index'
import { Route as AuthenticatedAdminJobsCreateIndexImport } from './routes/_authenticated/admin/jobs/create/index'
import { Route as AuthenticatedAdminJobsJobIdIndexImport } from './routes/_authenticated/admin/jobs/$jobId/index'
import { Route as AuthenticatedAdminInterviewerPersonaCreateIndexImport } from './routes/_authenticated/admin/interviewer-persona/create/index'
import { Route as AuthenticatedAdminInterviewerPersonaPersonaIdIndexImport } from './routes/_authenticated/admin/interviewer-persona/$personaId/index'
import { Route as AuthenticatedAdminApplicantsApplicantIdIndexImport } from './routes/_authenticated/admin/applicants/$applicantId/index'
import { Route as AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexImport } from './routes/_authenticated/admin/interviewer-persona/edit/$personaId/index'

// Create/Update Routes

const AuthenticatedRouteRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedIndexRoute = AuthenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const errors503Route = errors503Import.update({
  id: '/(errors)/503',
  path: '/503',
  getParentRoute: () => rootRoute,
} as any)

const errors500Route = errors500Import.update({
  id: '/(errors)/500',
  path: '/500',
  getParentRoute: () => rootRoute,
} as any)

const errors404Route = errors404Import.update({
  id: '/(errors)/404',
  path: '/404',
  getParentRoute: () => rootRoute,
} as any)

const errors403Route = errors403Import.update({
  id: '/(errors)/403',
  path: '/403',
  getParentRoute: () => rootRoute,
} as any)

const errors401Route = errors401Import.update({
  id: '/(errors)/401',
  path: '/401',
  getParentRoute: () => rootRoute,
} as any)

const authLoginRoute = authLoginImport.update({
  id: '/(auth)/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedAdminRouteRoute = AuthenticatedAdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthenticatedRouteRoute,
} as any)

const interviewInterviewIndexRoute = interviewInterviewIndexImport.update({
  id: '/(interview)/interview/',
  path: '/interview/',
  getParentRoute: () => rootRoute,
} as any)

const interviewInterviewOnboardingIndexRoute =
  interviewInterviewOnboardingIndexImport.update({
    id: '/(interview)/interview-onboarding/',
    path: '/interview-onboarding/',
    getParentRoute: () => rootRoute,
  } as any)

const interviewInterviewOnboardingSystemSetupIndexRoute =
  interviewInterviewOnboardingSystemSetupIndexImport.update({
    id: '/(interview)/interview-onboarding-system-setup/',
    path: '/interview-onboarding-system-setup/',
    getParentRoute: () => rootRoute,
  } as any)

const interviewInterviewCompleteIndexRoute =
  interviewInterviewCompleteIndexImport.update({
    id: '/(interview)/interview-complete/',
    path: '/interview-complete/',
    getParentRoute: () => rootRoute,
  } as any)

const candidateJobListIndexRoute = candidateJobListIndexImport.update({
  id: '/(candidate)/job-list/',
  path: '/job-list/',
  getParentRoute: () => rootRoute,
} as any)

const AuthenticatedAdminSettingsRouteRoute =
  AuthenticatedAdminSettingsRouteImport.update({
    id: '/settings',
    path: '/settings',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminSettingsIndexRoute =
  AuthenticatedAdminSettingsIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => AuthenticatedAdminSettingsRouteRoute,
  } as any)

const AuthenticatedAdminProfileIndexRoute =
  AuthenticatedAdminProfileIndexImport.update({
    id: '/profile/',
    path: '/profile/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminJobsIndexRoute =
  AuthenticatedAdminJobsIndexImport.update({
    id: '/jobs/',
    path: '/jobs/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminInterviewerPersonaIndexRoute =
  AuthenticatedAdminInterviewerPersonaIndexImport.update({
    id: '/interviewer-persona/',
    path: '/interviewer-persona/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminDashboardIndexRoute =
  AuthenticatedAdminDashboardIndexImport.update({
    id: '/dashboard/',
    path: '/dashboard/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminApplicantsIndexRoute =
  AuthenticatedAdminApplicantsIndexImport.update({
    id: '/applicants/',
    path: '/applicants/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const candidateJobApplicationJobIdIndexRoute =
  candidateJobApplicationJobIdIndexImport.update({
    id: '/(candidate)/job-application/$jobId/',
    path: '/job-application/$jobId/',
    getParentRoute: () => rootRoute,
  } as any)

const AuthenticatedAdminSettingsUserRoleRoute =
  AuthenticatedAdminSettingsUserRoleImport.update({
    id: '/user-role',
    path: '/user-role',
    getParentRoute: () => AuthenticatedAdminSettingsRouteRoute,
  } as any)

const AuthenticatedAdminSettingsUserManagementRoute =
  AuthenticatedAdminSettingsUserManagementImport.update({
    id: '/user-management',
    path: '/user-management',
    getParentRoute: () => AuthenticatedAdminSettingsRouteRoute,
  } as any)

const AuthenticatedAdminSettingsJobsRoute =
  AuthenticatedAdminSettingsJobsImport.update({
    id: '/jobs',
    path: '/jobs',
    getParentRoute: () => AuthenticatedAdminSettingsRouteRoute,
  } as any)

const AuthenticatedAdminJobsEditIndexRoute =
  AuthenticatedAdminJobsEditIndexImport.update({
    id: '/jobs/edit/',
    path: '/jobs/edit/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminJobsCreateIndexRoute =
  AuthenticatedAdminJobsCreateIndexImport.update({
    id: '/jobs/create/',
    path: '/jobs/create/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminJobsJobIdIndexRoute =
  AuthenticatedAdminJobsJobIdIndexImport.update({
    id: '/jobs/$jobId/',
    path: '/jobs/$jobId/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminInterviewerPersonaCreateIndexRoute =
  AuthenticatedAdminInterviewerPersonaCreateIndexImport.update({
    id: '/interviewer-persona/create/',
    path: '/interviewer-persona/create/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminInterviewerPersonaPersonaIdIndexRoute =
  AuthenticatedAdminInterviewerPersonaPersonaIdIndexImport.update({
    id: '/interviewer-persona/$personaId/',
    path: '/interviewer-persona/$personaId/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminApplicantsApplicantIdIndexRoute =
  AuthenticatedAdminApplicantsApplicantIdIndexImport.update({
    id: '/applicants/$applicantId/',
    path: '/applicants/$applicantId/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

const AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexRoute =
  AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexImport.update({
    id: '/interviewer-persona/edit/$personaId/',
    path: '/interviewer-persona/edit/$personaId/',
    getParentRoute: () => AuthenticatedAdminRouteRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/admin': {
      id: '/_authenticated/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AuthenticatedAdminRouteImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/(auth)/login': {
      id: '/(auth)/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof authLoginImport
      parentRoute: typeof rootRoute
    }
    '/(errors)/401': {
      id: '/(errors)/401'
      path: '/401'
      fullPath: '/401'
      preLoaderRoute: typeof errors401Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/403': {
      id: '/(errors)/403'
      path: '/403'
      fullPath: '/403'
      preLoaderRoute: typeof errors403Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/404': {
      id: '/(errors)/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof errors404Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/500': {
      id: '/(errors)/500'
      path: '/500'
      fullPath: '/500'
      preLoaderRoute: typeof errors500Import
      parentRoute: typeof rootRoute
    }
    '/(errors)/503': {
      id: '/(errors)/503'
      path: '/503'
      fullPath: '/503'
      preLoaderRoute: typeof errors503Import
      parentRoute: typeof rootRoute
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexImport
      parentRoute: typeof AuthenticatedRouteImport
    }
    '/_authenticated/admin/settings': {
      id: '/_authenticated/admin/settings'
      path: '/settings'
      fullPath: '/admin/settings'
      preLoaderRoute: typeof AuthenticatedAdminSettingsRouteImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/(candidate)/job-list/': {
      id: '/(candidate)/job-list/'
      path: '/job-list'
      fullPath: '/job-list'
      preLoaderRoute: typeof candidateJobListIndexImport
      parentRoute: typeof rootRoute
    }
    '/(interview)/interview-complete/': {
      id: '/(interview)/interview-complete/'
      path: '/interview-complete'
      fullPath: '/interview-complete'
      preLoaderRoute: typeof interviewInterviewCompleteIndexImport
      parentRoute: typeof rootRoute
    }
    '/(interview)/interview-onboarding-system-setup/': {
      id: '/(interview)/interview-onboarding-system-setup/'
      path: '/interview-onboarding-system-setup'
      fullPath: '/interview-onboarding-system-setup'
      preLoaderRoute: typeof interviewInterviewOnboardingSystemSetupIndexImport
      parentRoute: typeof rootRoute
    }
    '/(interview)/interview-onboarding/': {
      id: '/(interview)/interview-onboarding/'
      path: '/interview-onboarding'
      fullPath: '/interview-onboarding'
      preLoaderRoute: typeof interviewInterviewOnboardingIndexImport
      parentRoute: typeof rootRoute
    }
    '/(interview)/interview/': {
      id: '/(interview)/interview/'
      path: '/interview'
      fullPath: '/interview'
      preLoaderRoute: typeof interviewInterviewIndexImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/admin/settings/jobs': {
      id: '/_authenticated/admin/settings/jobs'
      path: '/jobs'
      fullPath: '/admin/settings/jobs'
      preLoaderRoute: typeof AuthenticatedAdminSettingsJobsImport
      parentRoute: typeof AuthenticatedAdminSettingsRouteImport
    }
    '/_authenticated/admin/settings/user-management': {
      id: '/_authenticated/admin/settings/user-management'
      path: '/user-management'
      fullPath: '/admin/settings/user-management'
      preLoaderRoute: typeof AuthenticatedAdminSettingsUserManagementImport
      parentRoute: typeof AuthenticatedAdminSettingsRouteImport
    }
    '/_authenticated/admin/settings/user-role': {
      id: '/_authenticated/admin/settings/user-role'
      path: '/user-role'
      fullPath: '/admin/settings/user-role'
      preLoaderRoute: typeof AuthenticatedAdminSettingsUserRoleImport
      parentRoute: typeof AuthenticatedAdminSettingsRouteImport
    }
    '/(candidate)/job-application/$jobId/': {
      id: '/(candidate)/job-application/$jobId/'
      path: '/job-application/$jobId'
      fullPath: '/job-application/$jobId'
      preLoaderRoute: typeof candidateJobApplicationJobIdIndexImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated/admin/applicants/': {
      id: '/_authenticated/admin/applicants/'
      path: '/applicants'
      fullPath: '/admin/applicants'
      preLoaderRoute: typeof AuthenticatedAdminApplicantsIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/dashboard/': {
      id: '/_authenticated/admin/dashboard/'
      path: '/dashboard'
      fullPath: '/admin/dashboard'
      preLoaderRoute: typeof AuthenticatedAdminDashboardIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/interviewer-persona/': {
      id: '/_authenticated/admin/interviewer-persona/'
      path: '/interviewer-persona'
      fullPath: '/admin/interviewer-persona'
      preLoaderRoute: typeof AuthenticatedAdminInterviewerPersonaIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/jobs/': {
      id: '/_authenticated/admin/jobs/'
      path: '/jobs'
      fullPath: '/admin/jobs'
      preLoaderRoute: typeof AuthenticatedAdminJobsIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/profile/': {
      id: '/_authenticated/admin/profile/'
      path: '/profile'
      fullPath: '/admin/profile'
      preLoaderRoute: typeof AuthenticatedAdminProfileIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/settings/': {
      id: '/_authenticated/admin/settings/'
      path: '/'
      fullPath: '/admin/settings/'
      preLoaderRoute: typeof AuthenticatedAdminSettingsIndexImport
      parentRoute: typeof AuthenticatedAdminSettingsRouteImport
    }
    '/_authenticated/admin/applicants/$applicantId/': {
      id: '/_authenticated/admin/applicants/$applicantId/'
      path: '/applicants/$applicantId'
      fullPath: '/admin/applicants/$applicantId'
      preLoaderRoute: typeof AuthenticatedAdminApplicantsApplicantIdIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/interviewer-persona/$personaId/': {
      id: '/_authenticated/admin/interviewer-persona/$personaId/'
      path: '/interviewer-persona/$personaId'
      fullPath: '/admin/interviewer-persona/$personaId'
      preLoaderRoute: typeof AuthenticatedAdminInterviewerPersonaPersonaIdIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/interviewer-persona/create/': {
      id: '/_authenticated/admin/interviewer-persona/create/'
      path: '/interviewer-persona/create'
      fullPath: '/admin/interviewer-persona/create'
      preLoaderRoute: typeof AuthenticatedAdminInterviewerPersonaCreateIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/jobs/$jobId/': {
      id: '/_authenticated/admin/jobs/$jobId/'
      path: '/jobs/$jobId'
      fullPath: '/admin/jobs/$jobId'
      preLoaderRoute: typeof AuthenticatedAdminJobsJobIdIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/jobs/create/': {
      id: '/_authenticated/admin/jobs/create/'
      path: '/jobs/create'
      fullPath: '/admin/jobs/create'
      preLoaderRoute: typeof AuthenticatedAdminJobsCreateIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/jobs/edit/': {
      id: '/_authenticated/admin/jobs/edit/'
      path: '/jobs/edit'
      fullPath: '/admin/jobs/edit'
      preLoaderRoute: typeof AuthenticatedAdminJobsEditIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
    '/_authenticated/admin/interviewer-persona/edit/$personaId/': {
      id: '/_authenticated/admin/interviewer-persona/edit/$personaId/'
      path: '/interviewer-persona/edit/$personaId'
      fullPath: '/admin/interviewer-persona/edit/$personaId'
      preLoaderRoute: typeof AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexImport
      parentRoute: typeof AuthenticatedAdminRouteImport
    }
  }
}

// Create and export the route tree

interface AuthenticatedAdminSettingsRouteRouteChildren {
  AuthenticatedAdminSettingsJobsRoute: typeof AuthenticatedAdminSettingsJobsRoute
  AuthenticatedAdminSettingsUserManagementRoute: typeof AuthenticatedAdminSettingsUserManagementRoute
  AuthenticatedAdminSettingsUserRoleRoute: typeof AuthenticatedAdminSettingsUserRoleRoute
  AuthenticatedAdminSettingsIndexRoute: typeof AuthenticatedAdminSettingsIndexRoute
}

const AuthenticatedAdminSettingsRouteRouteChildren: AuthenticatedAdminSettingsRouteRouteChildren =
  {
    AuthenticatedAdminSettingsJobsRoute: AuthenticatedAdminSettingsJobsRoute,
    AuthenticatedAdminSettingsUserManagementRoute:
      AuthenticatedAdminSettingsUserManagementRoute,
    AuthenticatedAdminSettingsUserRoleRoute:
      AuthenticatedAdminSettingsUserRoleRoute,
    AuthenticatedAdminSettingsIndexRoute: AuthenticatedAdminSettingsIndexRoute,
  }

const AuthenticatedAdminSettingsRouteRouteWithChildren =
  AuthenticatedAdminSettingsRouteRoute._addFileChildren(
    AuthenticatedAdminSettingsRouteRouteChildren,
  )

interface AuthenticatedAdminRouteRouteChildren {
  AuthenticatedAdminSettingsRouteRoute: typeof AuthenticatedAdminSettingsRouteRouteWithChildren
  AuthenticatedAdminApplicantsIndexRoute: typeof AuthenticatedAdminApplicantsIndexRoute
  AuthenticatedAdminDashboardIndexRoute: typeof AuthenticatedAdminDashboardIndexRoute
  AuthenticatedAdminInterviewerPersonaIndexRoute: typeof AuthenticatedAdminInterviewerPersonaIndexRoute
  AuthenticatedAdminJobsIndexRoute: typeof AuthenticatedAdminJobsIndexRoute
  AuthenticatedAdminProfileIndexRoute: typeof AuthenticatedAdminProfileIndexRoute
  AuthenticatedAdminApplicantsApplicantIdIndexRoute: typeof AuthenticatedAdminApplicantsApplicantIdIndexRoute
  AuthenticatedAdminInterviewerPersonaPersonaIdIndexRoute: typeof AuthenticatedAdminInterviewerPersonaPersonaIdIndexRoute
  AuthenticatedAdminInterviewerPersonaCreateIndexRoute: typeof AuthenticatedAdminInterviewerPersonaCreateIndexRoute
  AuthenticatedAdminJobsJobIdIndexRoute: typeof AuthenticatedAdminJobsJobIdIndexRoute
  AuthenticatedAdminJobsCreateIndexRoute: typeof AuthenticatedAdminJobsCreateIndexRoute
  AuthenticatedAdminJobsEditIndexRoute: typeof AuthenticatedAdminJobsEditIndexRoute
  AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexRoute: typeof AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexRoute
}

const AuthenticatedAdminRouteRouteChildren: AuthenticatedAdminRouteRouteChildren =
  {
    AuthenticatedAdminSettingsRouteRoute:
      AuthenticatedAdminSettingsRouteRouteWithChildren,
    AuthenticatedAdminApplicantsIndexRoute:
      AuthenticatedAdminApplicantsIndexRoute,
    AuthenticatedAdminDashboardIndexRoute:
      AuthenticatedAdminDashboardIndexRoute,
    AuthenticatedAdminInterviewerPersonaIndexRoute:
      AuthenticatedAdminInterviewerPersonaIndexRoute,
    AuthenticatedAdminJobsIndexRoute: AuthenticatedAdminJobsIndexRoute,
    AuthenticatedAdminProfileIndexRoute: AuthenticatedAdminProfileIndexRoute,
    AuthenticatedAdminApplicantsApplicantIdIndexRoute:
      AuthenticatedAdminApplicantsApplicantIdIndexRoute,
    AuthenticatedAdminInterviewerPersonaPersonaIdIndexRoute:
      AuthenticatedAdminInterviewerPersonaPersonaIdIndexRoute,
    AuthenticatedAdminInterviewerPersonaCreateIndexRoute:
      AuthenticatedAdminInterviewerPersonaCreateIndexRoute,
    AuthenticatedAdminJobsJobIdIndexRoute:
      AuthenticatedAdminJobsJobIdIndexRoute,
    AuthenticatedAdminJobsCreateIndexRoute:
      AuthenticatedAdminJobsCreateIndexRoute,
    AuthenticatedAdminJobsEditIndexRoute: AuthenticatedAdminJobsEditIndexRoute,
    AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexRoute:
      AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexRoute,
  }

const AuthenticatedAdminRouteRouteWithChildren =
  AuthenticatedAdminRouteRoute._addFileChildren(
    AuthenticatedAdminRouteRouteChildren,
  )

interface AuthenticatedRouteRouteChildren {
  AuthenticatedAdminRouteRoute: typeof AuthenticatedAdminRouteRouteWithChildren
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
}

const AuthenticatedRouteRouteChildren: AuthenticatedRouteRouteChildren = {
  AuthenticatedAdminRouteRoute: AuthenticatedAdminRouteRouteWithChildren,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
}

const AuthenticatedRouteRouteWithChildren =
  AuthenticatedRouteRoute._addFileChildren(AuthenticatedRouteRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteRouteWithChildren
  '/admin': typeof AuthenticatedAdminRouteRouteWithChildren
  '/login': typeof authLoginRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/': typeof AuthenticatedIndexRoute
  '/admin/settings': typeof AuthenticatedAdminSettingsRouteRouteWithChildren
  '/job-list': typeof candidateJobListIndexRoute
  '/interview-complete': typeof interviewInterviewCompleteIndexRoute
  '/interview-onboarding-system-setup': typeof interviewInterviewOnboardingSystemSetupIndexRoute
  '/interview-onboarding': typeof interviewInterviewOnboardingIndexRoute
  '/interview': typeof interviewInterviewIndexRoute
  '/admin/settings/jobs': typeof AuthenticatedAdminSettingsJobsRoute
  '/admin/settings/user-management': typeof AuthenticatedAdminSettingsUserManagementRoute
  '/admin/settings/user-role': typeof AuthenticatedAdminSettingsUserRoleRoute
  '/job-application/$jobId': typeof candidateJobApplicationJobIdIndexRoute
  '/admin/applicants': typeof AuthenticatedAdminApplicantsIndexRoute
  '/admin/dashboard': typeof AuthenticatedAdminDashboardIndexRoute
  '/admin/interviewer-persona': typeof AuthenticatedAdminInterviewerPersonaIndexRoute
  '/admin/jobs': typeof AuthenticatedAdminJobsIndexRoute
  '/admin/profile': typeof AuthenticatedAdminProfileIndexRoute
  '/admin/settings/': typeof AuthenticatedAdminSettingsIndexRoute
  '/admin/applicants/$applicantId': typeof AuthenticatedAdminApplicantsApplicantIdIndexRoute
  '/admin/interviewer-persona/$personaId': typeof AuthenticatedAdminInterviewerPersonaPersonaIdIndexRoute
  '/admin/interviewer-persona/create': typeof AuthenticatedAdminInterviewerPersonaCreateIndexRoute
  '/admin/jobs/$jobId': typeof AuthenticatedAdminJobsJobIdIndexRoute
  '/admin/jobs/create': typeof AuthenticatedAdminJobsCreateIndexRoute
  '/admin/jobs/edit': typeof AuthenticatedAdminJobsEditIndexRoute
  '/admin/interviewer-persona/edit/$personaId': typeof AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexRoute
}

export interface FileRoutesByTo {
  '/admin': typeof AuthenticatedAdminRouteRouteWithChildren
  '/login': typeof authLoginRoute
  '/401': typeof errors401Route
  '/403': typeof errors403Route
  '/404': typeof errors404Route
  '/500': typeof errors500Route
  '/503': typeof errors503Route
  '/': typeof AuthenticatedIndexRoute
  '/job-list': typeof candidateJobListIndexRoute
  '/interview-complete': typeof interviewInterviewCompleteIndexRoute
  '/interview-onboarding-system-setup': typeof interviewInterviewOnboardingSystemSetupIndexRoute
  '/interview-onboarding': typeof interviewInterviewOnboardingIndexRoute
  '/interview': typeof interviewInterviewIndexRoute
  '/admin/settings/jobs': typeof AuthenticatedAdminSettingsJobsRoute
  '/admin/settings/user-management': typeof AuthenticatedAdminSettingsUserManagementRoute
  '/admin/settings/user-role': typeof AuthenticatedAdminSettingsUserRoleRoute
  '/job-application/$jobId': typeof candidateJobApplicationJobIdIndexRoute
  '/admin/applicants': typeof AuthenticatedAdminApplicantsIndexRoute
  '/admin/dashboard': typeof AuthenticatedAdminDashboardIndexRoute
  '/admin/interviewer-persona': typeof AuthenticatedAdminInterviewerPersonaIndexRoute
  '/admin/jobs': typeof AuthenticatedAdminJobsIndexRoute
  '/admin/profile': typeof AuthenticatedAdminProfileIndexRoute
  '/admin/settings': typeof AuthenticatedAdminSettingsIndexRoute
  '/admin/applicants/$applicantId': typeof AuthenticatedAdminApplicantsApplicantIdIndexRoute
  '/admin/interviewer-persona/$personaId': typeof AuthenticatedAdminInterviewerPersonaPersonaIdIndexRoute
  '/admin/interviewer-persona/create': typeof AuthenticatedAdminInterviewerPersonaCreateIndexRoute
  '/admin/jobs/$jobId': typeof AuthenticatedAdminJobsJobIdIndexRoute
  '/admin/jobs/create': typeof AuthenticatedAdminJobsCreateIndexRoute
  '/admin/jobs/edit': typeof AuthenticatedAdminJobsEditIndexRoute
  '/admin/interviewer-persona/edit/$personaId': typeof AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_authenticated': typeof AuthenticatedRouteRouteWithChildren
  '/_authenticated/admin': typeof AuthenticatedAdminRouteRouteWithChildren
  '/(auth)/login': typeof authLoginRoute
  '/(errors)/401': typeof errors401Route
  '/(errors)/403': typeof errors403Route
  '/(errors)/404': typeof errors404Route
  '/(errors)/500': typeof errors500Route
  '/(errors)/503': typeof errors503Route
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/admin/settings': typeof AuthenticatedAdminSettingsRouteRouteWithChildren
  '/(candidate)/job-list/': typeof candidateJobListIndexRoute
  '/(interview)/interview-complete/': typeof interviewInterviewCompleteIndexRoute
  '/(interview)/interview-onboarding-system-setup/': typeof interviewInterviewOnboardingSystemSetupIndexRoute
  '/(interview)/interview-onboarding/': typeof interviewInterviewOnboardingIndexRoute
  '/(interview)/interview/': typeof interviewInterviewIndexRoute
  '/_authenticated/admin/settings/jobs': typeof AuthenticatedAdminSettingsJobsRoute
  '/_authenticated/admin/settings/user-management': typeof AuthenticatedAdminSettingsUserManagementRoute
  '/_authenticated/admin/settings/user-role': typeof AuthenticatedAdminSettingsUserRoleRoute
  '/(candidate)/job-application/$jobId/': typeof candidateJobApplicationJobIdIndexRoute
  '/_authenticated/admin/applicants/': typeof AuthenticatedAdminApplicantsIndexRoute
  '/_authenticated/admin/dashboard/': typeof AuthenticatedAdminDashboardIndexRoute
  '/_authenticated/admin/interviewer-persona/': typeof AuthenticatedAdminInterviewerPersonaIndexRoute
  '/_authenticated/admin/jobs/': typeof AuthenticatedAdminJobsIndexRoute
  '/_authenticated/admin/profile/': typeof AuthenticatedAdminProfileIndexRoute
  '/_authenticated/admin/settings/': typeof AuthenticatedAdminSettingsIndexRoute
  '/_authenticated/admin/applicants/$applicantId/': typeof AuthenticatedAdminApplicantsApplicantIdIndexRoute
  '/_authenticated/admin/interviewer-persona/$personaId/': typeof AuthenticatedAdminInterviewerPersonaPersonaIdIndexRoute
  '/_authenticated/admin/interviewer-persona/create/': typeof AuthenticatedAdminInterviewerPersonaCreateIndexRoute
  '/_authenticated/admin/jobs/$jobId/': typeof AuthenticatedAdminJobsJobIdIndexRoute
  '/_authenticated/admin/jobs/create/': typeof AuthenticatedAdminJobsCreateIndexRoute
  '/_authenticated/admin/jobs/edit/': typeof AuthenticatedAdminJobsEditIndexRoute
  '/_authenticated/admin/interviewer-persona/edit/$personaId/': typeof AuthenticatedAdminInterviewerPersonaEditPersonaIdIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/admin'
    | '/login'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/admin/settings'
    | '/job-list'
    | '/interview-complete'
    | '/interview-onboarding-system-setup'
    | '/interview-onboarding'
    | '/interview'
    | '/admin/settings/jobs'
    | '/admin/settings/user-management'
    | '/admin/settings/user-role'
    | '/job-application/$jobId'
    | '/admin/applicants'
    | '/admin/dashboard'
    | '/admin/interviewer-persona'
    | '/admin/jobs'
    | '/admin/profile'
    | '/admin/settings/'
    | '/admin/applicants/$applicantId'
    | '/admin/interviewer-persona/$personaId'
    | '/admin/interviewer-persona/create'
    | '/admin/jobs/$jobId'
    | '/admin/jobs/create'
    | '/admin/jobs/edit'
    | '/admin/interviewer-persona/edit/$personaId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/admin'
    | '/login'
    | '/401'
    | '/403'
    | '/404'
    | '/500'
    | '/503'
    | '/'
    | '/job-list'
    | '/interview-complete'
    | '/interview-onboarding-system-setup'
    | '/interview-onboarding'
    | '/interview'
    | '/admin/settings/jobs'
    | '/admin/settings/user-management'
    | '/admin/settings/user-role'
    | '/job-application/$jobId'
    | '/admin/applicants'
    | '/admin/dashboard'
    | '/admin/interviewer-persona'
    | '/admin/jobs'
    | '/admin/profile'
    | '/admin/settings'
    | '/admin/applicants/$applicantId'
    | '/admin/interviewer-persona/$personaId'
    | '/admin/interviewer-persona/create'
    | '/admin/jobs/$jobId'
    | '/admin/jobs/create'
    | '/admin/jobs/edit'
    | '/admin/interviewer-persona/edit/$personaId'
  id:
    | '__root__'
    | '/_authenticated'
    | '/_authenticated/admin'
    | '/(auth)/login'
    | '/(errors)/401'
    | '/(errors)/403'
    | '/(errors)/404'
    | '/(errors)/500'
    | '/(errors)/503'
    | '/_authenticated/'
    | '/_authenticated/admin/settings'
    | '/(candidate)/job-list/'
    | '/(interview)/interview-complete/'
    | '/(interview)/interview-onboarding-system-setup/'
    | '/(interview)/interview-onboarding/'
    | '/(interview)/interview/'
    | '/_authenticated/admin/settings/jobs'
    | '/_authenticated/admin/settings/user-management'
    | '/_authenticated/admin/settings/user-role'
    | '/(candidate)/job-application/$jobId/'
    | '/_authenticated/admin/applicants/'
    | '/_authenticated/admin/dashboard/'
    | '/_authenticated/admin/interviewer-persona/'
    | '/_authenticated/admin/jobs/'
    | '/_authenticated/admin/profile/'
    | '/_authenticated/admin/settings/'
    | '/_authenticated/admin/applicants/$applicantId/'
    | '/_authenticated/admin/interviewer-persona/$personaId/'
    | '/_authenticated/admin/interviewer-persona/create/'
    | '/_authenticated/admin/jobs/$jobId/'
    | '/_authenticated/admin/jobs/create/'
    | '/_authenticated/admin/jobs/edit/'
    | '/_authenticated/admin/interviewer-persona/edit/$personaId/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthenticatedRouteRoute: typeof AuthenticatedRouteRouteWithChildren
  authLoginRoute: typeof authLoginRoute
  errors401Route: typeof errors401Route
  errors403Route: typeof errors403Route
  errors404Route: typeof errors404Route
  errors500Route: typeof errors500Route
  errors503Route: typeof errors503Route
  candidateJobListIndexRoute: typeof candidateJobListIndexRoute
  interviewInterviewCompleteIndexRoute: typeof interviewInterviewCompleteIndexRoute
  interviewInterviewOnboardingSystemSetupIndexRoute: typeof interviewInterviewOnboardingSystemSetupIndexRoute
  interviewInterviewOnboardingIndexRoute: typeof interviewInterviewOnboardingIndexRoute
  interviewInterviewIndexRoute: typeof interviewInterviewIndexRoute
  candidateJobApplicationJobIdIndexRoute: typeof candidateJobApplicationJobIdIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRouteRoute: AuthenticatedRouteRouteWithChildren,
  authLoginRoute: authLoginRoute,
  errors401Route: errors401Route,
  errors403Route: errors403Route,
  errors404Route: errors404Route,
  errors500Route: errors500Route,
  errors503Route: errors503Route,
  candidateJobListIndexRoute: candidateJobListIndexRoute,
  interviewInterviewCompleteIndexRoute: interviewInterviewCompleteIndexRoute,
  interviewInterviewOnboardingSystemSetupIndexRoute:
    interviewInterviewOnboardingSystemSetupIndexRoute,
  interviewInterviewOnboardingIndexRoute:
    interviewInterviewOnboardingIndexRoute,
  interviewInterviewIndexRoute: interviewInterviewIndexRoute,
  candidateJobApplicationJobIdIndexRoute:
    candidateJobApplicationJobIdIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_authenticated",
        "/(auth)/login",
        "/(errors)/401",
        "/(errors)/403",
        "/(errors)/404",
        "/(errors)/500",
        "/(errors)/503",
        "/(candidate)/job-list/",
        "/(interview)/interview-complete/",
        "/(interview)/interview-onboarding-system-setup/",
        "/(interview)/interview-onboarding/",
        "/(interview)/interview/",
        "/(candidate)/job-application/$jobId/"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated/route.tsx",
      "children": [
        "/_authenticated/admin",
        "/_authenticated/"
      ]
    },
    "/_authenticated/admin": {
      "filePath": "_authenticated/admin/route.tsx",
      "parent": "/_authenticated",
      "children": [
        "/_authenticated/admin/settings",
        "/_authenticated/admin/applicants/",
        "/_authenticated/admin/dashboard/",
        "/_authenticated/admin/interviewer-persona/",
        "/_authenticated/admin/jobs/",
        "/_authenticated/admin/profile/",
        "/_authenticated/admin/applicants/$applicantId/",
        "/_authenticated/admin/interviewer-persona/$personaId/",
        "/_authenticated/admin/interviewer-persona/create/",
        "/_authenticated/admin/jobs/$jobId/",
        "/_authenticated/admin/jobs/create/",
        "/_authenticated/admin/jobs/edit/",
        "/_authenticated/admin/interviewer-persona/edit/$personaId/"
      ]
    },
    "/(auth)/login": {
      "filePath": "(auth)/login.tsx"
    },
    "/(errors)/401": {
      "filePath": "(errors)/401.tsx"
    },
    "/(errors)/403": {
      "filePath": "(errors)/403.tsx"
    },
    "/(errors)/404": {
      "filePath": "(errors)/404.tsx"
    },
    "/(errors)/500": {
      "filePath": "(errors)/500.tsx"
    },
    "/(errors)/503": {
      "filePath": "(errors)/503.tsx"
    },
    "/_authenticated/": {
      "filePath": "_authenticated/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/admin/settings": {
      "filePath": "_authenticated/admin/settings/route.tsx",
      "parent": "/_authenticated/admin",
      "children": [
        "/_authenticated/admin/settings/jobs",
        "/_authenticated/admin/settings/user-management",
        "/_authenticated/admin/settings/user-role",
        "/_authenticated/admin/settings/"
      ]
    },
    "/(candidate)/job-list/": {
      "filePath": "(candidate)/job-list/index.tsx"
    },
    "/(interview)/interview-complete/": {
      "filePath": "(interview)/interview-complete/index.tsx"
    },
    "/(interview)/interview-onboarding-system-setup/": {
      "filePath": "(interview)/interview-onboarding-system-setup/index.tsx"
    },
    "/(interview)/interview-onboarding/": {
      "filePath": "(interview)/interview-onboarding/index.tsx"
    },
    "/(interview)/interview/": {
      "filePath": "(interview)/interview/index.tsx"
    },
    "/_authenticated/admin/settings/jobs": {
      "filePath": "_authenticated/admin/settings/jobs.tsx",
      "parent": "/_authenticated/admin/settings"
    },
    "/_authenticated/admin/settings/user-management": {
      "filePath": "_authenticated/admin/settings/user-management.tsx",
      "parent": "/_authenticated/admin/settings"
    },
    "/_authenticated/admin/settings/user-role": {
      "filePath": "_authenticated/admin/settings/user-role.tsx",
      "parent": "/_authenticated/admin/settings"
    },
    "/(candidate)/job-application/$jobId/": {
      "filePath": "(candidate)/job-application/$jobId/index.tsx"
    },
    "/_authenticated/admin/applicants/": {
      "filePath": "_authenticated/admin/applicants/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/dashboard/": {
      "filePath": "_authenticated/admin/dashboard/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/interviewer-persona/": {
      "filePath": "_authenticated/admin/interviewer-persona/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/jobs/": {
      "filePath": "_authenticated/admin/jobs/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/profile/": {
      "filePath": "_authenticated/admin/profile/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/settings/": {
      "filePath": "_authenticated/admin/settings/index.tsx",
      "parent": "/_authenticated/admin/settings"
    },
    "/_authenticated/admin/applicants/$applicantId/": {
      "filePath": "_authenticated/admin/applicants/$applicantId/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/interviewer-persona/$personaId/": {
      "filePath": "_authenticated/admin/interviewer-persona/$personaId/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/interviewer-persona/create/": {
      "filePath": "_authenticated/admin/interviewer-persona/create/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/jobs/$jobId/": {
      "filePath": "_authenticated/admin/jobs/$jobId/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/jobs/create/": {
      "filePath": "_authenticated/admin/jobs/create/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/jobs/edit/": {
      "filePath": "_authenticated/admin/jobs/edit/index.tsx",
      "parent": "/_authenticated/admin"
    },
    "/_authenticated/admin/interviewer-persona/edit/$personaId/": {
      "filePath": "_authenticated/admin/interviewer-persona/edit/$personaId/index.tsx",
      "parent": "/_authenticated/admin"
    }
  }
}
ROUTE_MANIFEST_END */
