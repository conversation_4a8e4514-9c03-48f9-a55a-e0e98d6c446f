import InterviewLayout from '@/components/layout/interview-layout';
import Interview from '@/features/interview/interview';
import SessionStorageManager, { SESSION_STORAGE_KEYS } from '@/utils/sessionStorage';
import { createFileRoute, redirect } from '@tanstack/react-router';

export const Route = createFileRoute('/(interview)/interview/')({
  // beforeLoad() {
  //   const candidateId = SessionStorageManager.getItem(SESSION_STORAGE_KEYS.CANDIDATE_DATA);
  //   if (!candidateId) {
  //     throw redirect({
  //       to: '/',
  //     });
  //   }
  // },
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <InterviewLayout>
      <Interview />
    </InterviewLayout>
  );
}
