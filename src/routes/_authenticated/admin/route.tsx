import { AppSidebar } from '@/components/layout/app-sidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { useUserProfileQuery } from '@/hooks/api/use-auth';
import { useAuthSlice } from '@/hooks/use-auth-slice';
import { cn } from '@/libs/utils';
import { createFileRoute, Outlet, redirect, useNavigate, useRouter } from '@tanstack/react-router';
import Cookies from 'js-cookie';
import { Suspense, useEffect } from 'react';

export const Route = createFileRoute('/_authenticated/admin')({
  beforeLoad: ({ location }) => {
    const isAuthenticated = Cookies.get(import.meta.env.VITE_PREVIA_ADMIN_ACCESS_TOKEN_KEY!);

    if (!isAuthenticated) {
      throw redirect({
        to: '/login',
        search: { redirect: location.href },
      });
    }

    if (location.pathname === '/admin' || location.pathname === '/admin/') {
      throw redirect({ to: '/admin/dashboard' });
    }
  },
  component: AdminLayout,
  notFoundComponent: AuthenticatedNotFound,
});

function AdminLayout() {
  const { isAuthenticated } = useAuthSlice();
  const defaultOpen = Cookies.get('sidebar_state') !== 'collapsed';
  const { isLoading, error } = useUserProfileQuery();

  if (!isAuthenticated()) return null;
  if (isLoading) {
    return (
      <div className='flex h-screen items-center justify-center'>
        <div className='border-primary h-8 w-8 animate-spin rounded-full border-b-2'></div>
      </div>
    );
  }
  if (error) return null;

  return (
    <Suspense
      fallback={
        <div className='flex h-screen items-center justify-center'>
          <div className='border-primary h-8 w-8 animate-spin rounded-full border-b-2'></div>
        </div>
      }
    >
      <SidebarProvider defaultOpen={defaultOpen} className='font-manrope'>
        <AppSidebar />
        <div
          id='content'
          className={cn(
            'ml-auto w-full max-w-full',
            'peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]',
            'peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]',
            'sm:transition-[width] sm:duration-200 sm:ease-linear',
            'flex flex-col',
            'group-data-[scroll-locked=1]/body:h-full',
            'has-[main.fixed-main]:group-data-[scroll-locked=1]/body:h-svh'
          )}
        >
          <Outlet />
        </div>
      </SidebarProvider>
    </Suspense>
  );
}

function AuthenticatedNotFound() {
  const navigate = useNavigate();
  const { history } = useRouter();

  useEffect(() => {
    // Redirect back to previous page or dashboard
    if (history.length > 1) {
      history.go(-1);
    } else {
      navigate({ to: '/admin/dashboard' });
    }
  }, []);

  return (
    <div className='flex h-screen items-center justify-center'>
      <div className='text-center'>
        <p>Redirecting...</p>
      </div>
    </div>
  );
}
