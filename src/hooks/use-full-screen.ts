import { useCallback, useEffect, useState } from 'react';

// Types for different browser implementations of the Fullscreen API
interface FullscreenAPI {
  requestFullscreen?: () => Promise<void>;
  webkitRequestFullscreen?: () => Promise<void>;
  mozRequestFullScreen?: () => Promise<void>;
  msRequestFullscreen?: () => Promise<void>;
}

// Extending the Document type to include vendor-prefixed fullscreen APIs
interface FullscreenDocument extends Document {
  fullscreenEnabled: boolean;
  webkitFullscreenEnabled?: boolean;
  mozFullScreenEnabled?: boolean;
  msFullscreenEnabled?: boolean;

  readonly fullscreenElement: Element | null;
  readonly webkitFullscreenElement?: Element | null;
  readonly mozFullScreenElement?: Element | null;
  readonly msFullscreenElement?: Element | null;

  exitFullscreen: () => Promise<void>;
  webkitExitFullscreen?: () => Promise<void>;
  mozCancelFullScreen?: () => Promise<void>;
  msExitFullscreen?: () => Promise<void>;
}

interface FullscreenError {
  name: string;
  message: string;
}

interface UseFullScreenReturn {
  isFullScreen: boolean;
  toggleFullScreen: (element?: HTMLElement) => Promise<void>;
  enterFullScreen: (element?: HTMLElement) => Promise<void>;
  exitFullScreen: () => Promise<void>;
  error: FullscreenError | null;
}

interface UseFullScreenOptions {
  onError?: (error: FullscreenError) => void;
  onFullScreenChange?: (isFullScreen: boolean) => void;
}

const useFullScreen = (options?: UseFullScreenOptions): UseFullScreenReturn => {
  const [error, setError] = useState<FullscreenError | null>(null);

  const [isFullScreen, setIsFullScreen] = useState<boolean>(false);

  const documentRef = document as FullscreenDocument;

  // Type guard for checking fullscreen support
  const isFullscreenEnabled = (): boolean => {
    return !!(
      documentRef.fullscreenEnabled ||
      documentRef.webkitFullscreenEnabled ||
      documentRef.mozFullScreenEnabled ||
      documentRef.msFullscreenEnabled
    );
  };

  // Get the current fullscreen element with type safety
  const getFullscreenElement = (): Element | null | undefined => {
    return (
      documentRef.fullscreenElement ||
      documentRef.webkitFullscreenElement ||
      documentRef.mozFullScreenElement ||
      documentRef.msFullscreenElement
    );
  };

  // Request fullscreen with type checking
  const requestFullscreen = async (element: HTMLElement & FullscreenAPI): Promise<void> => {
    try {
      if (!isFullscreenEnabled()) {
        throw new Error('Fullscreen is not supported in this environment');
      }

      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      }
    } catch (err) {
      const fullscreenError: FullscreenError = {
        name: 'FullscreenError',
        message: err instanceof Error ? err.message : 'Failed to enter fullscreen',
      };
      setError(fullscreenError);
      options?.onError?.(fullscreenError);
    }
  };

  // Exit fullscreen with type checking
  const exitFullScreen = async (): Promise<void> => {
    console.log('exitFullScreen');

    try {
      if (documentRef.exitFullscreen) {
        await documentRef.exitFullscreen();
      } else if (documentRef.webkitExitFullscreen) {
        await documentRef.webkitExitFullscreen();
      } else if (documentRef.mozCancelFullScreen) {
        await documentRef.mozCancelFullScreen();
      } else if (documentRef.msExitFullscreen) {
        await documentRef.msExitFullscreen();
      }
    } catch (err) {
      const fullscreenError: FullscreenError = {
        name: 'FullscreenError',
        message: err instanceof Error ? err.message : 'Failed to exit fullscreen',
      };
      setError(fullscreenError);
      options?.onError?.(fullscreenError);
    }
  };

  // Handle fullscreen change events
  const handleFullscreenChange = useCallback(() => {
    const fullscreenActive = !!getFullscreenElement();
    setIsFullScreen(fullscreenActive);
    options?.onFullScreenChange?.(fullscreenActive);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [options]);

  // Add event listeners with proper cleanup
  useEffect(() => {
    const events: string[] = [
      'fullscreenchange',
      'webkitfullscreenchange',
      'mozfullscreenchange',
      'MSFullscreenChange',
    ];

    events.forEach((event) => {
      document.addEventListener(event, handleFullscreenChange);
    });

    return () => {
      events.forEach((event) => {
        document.removeEventListener(event, handleFullscreenChange);
      });
    };
  }, [handleFullscreenChange]);

  const enterFullScreen = async (element?: HTMLElement): Promise<void> => {
    setError(null);
    const targetElement = (element ?? document.documentElement) as HTMLElement & FullscreenAPI;
    await requestFullscreen(targetElement);
  };

  const toggleFullScreen = async (element?: HTMLElement): Promise<void> => {
    if (isFullScreen) {
      await exitFullScreen();
    } else {
      await enterFullScreen(element);
    }
  };

  return {
    isFullScreen,
    toggleFullScreen,
    enterFullScreen,
    exitFullScreen,
    error,
  };
};

export default useFullScreen;
