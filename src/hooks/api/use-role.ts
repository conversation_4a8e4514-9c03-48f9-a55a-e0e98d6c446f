import { usersKeys } from './use-user';
import { getAllRoles, createRole, updateRole, deleteRole } from '@/services/roles';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const roleKeys = {
  all: ['roles'] as const,
  lists: (params?: RolesParams) => [...roleKeys.all, 'list', ...(params ? [params] : [])] as const,
  list: (filters: string) => [...roleKeys.lists(), { filters }] as const,
};

export function useRolesQuery(
  params: RolesParams = { number: 1, limit: 10 },
  options = { enabled: true }
) {
  return useQuery({
    queryKey: roleKeys.lists(params),
    queryFn: async () => {
      const response = await getAllRoles(params);
      return response.data.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateRoleMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['create-role'],
    mutationFn: async (payload: RoleSavePayload) => {
      const response = await createRole(payload);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
      toast.success('Role created successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to create role';
        toast.error(errorMessage);
      }
    },
  });
}

export function useUpdateRoleMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['update-role'],
    mutationFn: async ({ roleId, payload }: { roleId: number; payload: RoleSavePayload }) => {
      const response = await updateRole(roleId, payload);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
      toast.success('Role updated successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to create role';
        toast.error(errorMessage);
      }
    },
  });
}

export function useDeleteRoleMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ['delete-role'],
    mutationFn: async (roleId: number) => {
      const response = await deleteRole(roleId);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: [roleKeys.lists(), usersKeys.lists] });
      toast.success('Role deleted successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to delete role';
        toast.error(errorMessage);
      }
    },
  });
}
