import {
  getPersonaPreviewCVs,
  getPersonaPreviewCVUrl,
  prepareLiveInterview,
  uploadPersonaPreviewCV,
} from '@/services/prompt';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const promptKeys = {
  all: ['prompt'] as const,
  getPreviewCVUrl: (personaId: string, cvId: string) =>
    [...promptKeys.all, 'get-preview-cv-url', personaId, cvId] as const,
  getPreviewCVs: (personaId: string) => [...promptKeys.all, 'get-preview-cvs', personaId] as const,
};

export function usePrepareLiveInterviewMutation() {
  return useMutation({
    mutationKey: ['prepare-live-interview'],
    mutationFn: async (payload: LiveInterviewPayload) => {
      const response = await prepareLiveInterview(payload);
      return response.data;
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to prepare live interview';
        toast.error(errorMessage);
      }
    },
  });
}

export function useUploadPersonaPreviewCVMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['upload-preview-cv'],
    mutationFn: async (payload: { personaId: string; files: File[] }) => {
      const response = await uploadPersonaPreviewCV(payload.personaId, payload.files);
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: promptKeys.getPreviewCVs(variables.personaId) });
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to upload CV';
        toast.error(errorMessage);
      }
    },
  });
}

export function useGetPersonaPreviewCVUrlQuery(options: { personaId: string; cvId: string }) {
  return useQuery({
    queryKey: promptKeys.getPreviewCVUrl(options.personaId, options.cvId),
    queryFn: async () => {
      const response = await getPersonaPreviewCVUrl(options.personaId, options.cvId);
      return response.data;
    },
    enabled: !!options.personaId && !!options.cvId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useGetPersonaPreviewCVsQuery(personaId: string, options = { enabled: true }) {
  return useQuery({
    queryKey: promptKeys.getPreviewCVs(personaId),
    queryFn: async () => {
      const response = await getPersonaPreviewCVs(personaId);
      return response.data;
    },
    enabled: options.enabled && !!personaId,
    staleTime: 5 * 60 * 1000,
  });
}
