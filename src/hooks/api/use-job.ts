import {
  createJob,
  getJobs,
  getJob,
  getActiveJobs,
  getJobById,
  getGenerateJobDescription,
  updateJob,
  getJobDetailsForAdmin,
  getJobCandidates,
  deleteJob,
  getJobR<PERSON>s,
  createJob<PERSON>ole,
  updateJob<PERSON>ole,
  deleteJob<PERSON>ole,
} from '@/services/job';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const jobKeys = {
  all: ['job'] as const,
  lists: (params?: GetJobsParams) =>
    params ? ([...jobKeys.all, 'list', params] as const) : ([...jobKeys.all, 'list'] as const),
  details: () => [...jobKeys.all, 'detail'] as const,
  adminDetails: (id: string) => [...jobKeys.all, 'admin-detail', id] as const,
  detail: (id: string) => [...jobKeys.details(), id] as const,
  candidates: (jobId: string, params: GetJobsParams) =>
    [...jobKeys.all, 'candidates', jobId, ...(params ? [params] : [])] as const,
};

export function useCreateJobMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['create-job'],
    mutationFn: async (payload: JobInformation) => {
      const response = await createJob(payload);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: jobKeys.lists() });
      // toast.success('Job created successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to create job';
        toast.error(errorMessage);
      }
    },
  });
}

export function useJobsQuery(params: GetJobsParams, options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.lists(params),
    queryFn: async () => {
      const response = await getJobs(params);
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}

export function useJobByIdQuery(id: string, options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.detail(id),
    queryFn: async () => {
      const response = await getJobById(id);
      return response.data;
    },
    enabled: options.enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useActiveJobsQuery(options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.lists(),
    queryFn: async () => {
      const response = await getActiveJobs();
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}

export function useJobQuery(id: string, options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.detail(id),
    queryFn: async () => {
      const response = await getJob(id);
      return response.data;
    },
    enabled: options.enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export const useUpdateJobMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['update-job'],
    mutationFn: async ({ id, payload }: { id: string; payload: Partial<JobInformation> }) => {
      const response = await updateJob(id, payload);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: [jobKeys.lists(), jobKeys.all] });
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to update job';
        toast.error(errorMessage);
      }
    },
  });
};

export function useGenerateJobDescription(id: string, options = { enabled: false }) {
  return useQuery({
    queryKey: ['generate-job-description', id],
    queryFn: async () => {
      const response = await getGenerateJobDescription(id);
      return response.data;
    },
    enabled: options.enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useJobDetailsForAdminQuery(jobId: string, options = { enabled: true }) {
  return useQuery({
    queryKey: jobKeys.adminDetails(jobId),
    queryFn: async () => {
      const response = await getJobDetailsForAdmin(jobId);
      return response.data;
    },
    enabled: options.enabled && !!jobId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useJobCandidatesQuery(
  payload: { jobId: string; params: GetJobsParams },
  options = { enabled: true }
) {
  return useQuery({
    queryKey: jobKeys.candidates(payload.jobId, payload.params),
    queryFn: async () => {
      const response = await getJobCandidates(payload.jobId, payload.params);
      return response.data;
    },
    enabled: options.enabled && !!payload.jobId,
    staleTime: 5 * 60 * 1000,
  });
}
export function useDeleteJobMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ['delete-job'],
    mutationFn: async (id: string) => {
      const response = await deleteJob(id);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: jobKeys.lists() });
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to delete job';
        toast.error(errorMessage);
      }
    },
  });
}

// Job Roles
export function useJobRolesQuery(
  params: GetJobsParams = { limit: 10, offset: 0 },
  options = { enabled: true }
) {
  return useQuery({
    queryKey: ['job-roles', params],
    queryFn: async () => {
      const response = await getJobRoles(params);
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateJobRoleMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['create-job-role'],
    mutationFn: async (payload: JobRolePayload) => {
      const response = await createJobRole(payload);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: ['job-roles'] });
      toast.success('Job role created successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to create job role';
        toast.error(errorMessage);
      }
    },
  });
}

export function useUpdateJobRoleMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['update-job-role'],
    mutationFn: async ({ roleId, payload }: { roleId: string; payload: JobRolePayload }) => {
      const response = await updateJobRole(roleId, payload);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: ['job-roles'] });
      toast.success('Job role updated successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to update job role';
        toast.error(errorMessage);
      }
    },
  });
}

export function useDeleteJobRoleMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ['delete-job-role'],
    mutationFn: async (roleId: string) => {
      const response = await deleteJobRole(roleId);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: ['job-roles'] });
      toast.success('Job role deleted successfully!');
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to delete job role';
        toast.error(errorMessage);
      }
    },
  });
}
