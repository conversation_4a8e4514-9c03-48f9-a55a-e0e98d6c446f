import {
  attachPersonaToJob,
  create<PERSON>erson<PERSON>,
  deletePersona,
  generatePrompt,
  getJobPersonas,
  getJobsByPersona,
  getPersonaById,
  getPersonas,
  updatePersona,
} from '@/services/persona';
import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { toast } from 'sonner';

export const personaKeys = {
  all: ['persona'] as const,
  lists: (params?: ListingParams) =>
    params ? ([...personaKeys.all, 'list', params] as const) : ([...personaKeys.all] as const),
  jobPersonas: (jobId: string) => [...personaKeys.all, 'job-personas', jobId] as const,
  details: (id: string) => [...personaKeys.all, 'detail', id] as const,
  jobs: (personaId: string, params: ListingParams) =>
    [...personaKeys.all, 'jobs', personaId, params] as const,
};

export function usePersonasQuery(params?: ListingParams, options = { enabled: true }) {
  return useQuery({
    queryKey: personaKeys.lists(params),
    queryFn: async () => {
      const response = await getPersonas(params);
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}

export function usePersonasInfiniteQuery(params?: ListingParams, options = { enabled: true }) {
  return useInfiniteQuery({
    queryKey: personaKeys.lists(params),
    queryFn: async ({ pageParam = 0 }) => {
      const response = await getPersonas({ ...params, offset: pageParam });
      return response.data;
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      const nextOffset = lastPage.offset + lastPage.limit;
      return nextOffset < lastPage.total ? nextOffset : undefined;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
}

export function useAttachPersonaToJobMutation() {
  return useMutation({
    mutationFn: async (payload: { jobId: string; list: { personas: JobPersona[] } }) => {
      const response = await attachPersonaToJob(payload);
      return response.data;
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to attach persona to job';
        toast.error(errorMessage);
      }
    },
  });
}

export function useGetJobPersonasQuery(jobId: string, options = { enabled: true }) {
  return useQuery({
    queryKey: personaKeys.jobPersonas(jobId),
    queryFn: async () => {
      const response = await getJobPersonas(jobId);
      return response.data;
    },
    enabled: options.enabled && !!jobId,
    staleTime: 5 * 60 * 1000,
  });
}

export function usePersonaDetailsQuery(personaId: string, options = { enabled: true }) {
  return useQuery({
    queryKey: personaKeys.details(personaId),
    queryFn: async () => {
      const response = await getPersonaById(personaId);
      return response.data;
    },
    enabled: options.enabled && !!personaId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useGeneratePromptMutation() {
  return useMutation({
    mutationFn: async (payload: PromptGenerationPayload) => {
      const response = await generatePrompt(payload);
      return response.data;
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to generate prompt';
        toast.error(errorMessage);
      }
    },
  });
}

export function useDeletePersonaMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['delete-persona'],
    mutationFn: async (id: string) => {
      const response = await deletePersona(id);
      return response.data;
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({ queryKey: personaKeys.all });
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to delete persona';
        toast.error(errorMessage);
      }
    },
  });
}

export function useGetJobsByPersonaQuery(
  personaId: string,
  params: ListingParams,
  options = { enabled: true }
) {
  return useQuery({
    queryKey: personaKeys.jobs(personaId, params),
    queryFn: async () => {
      const response = await getJobsByPersona(personaId, params);
      return response.data;
    },
    enabled: options.enabled && !!personaId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreatePersonaMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: PersonaCreatePayload) => {
      const response = await createPersona(payload);
      return response.data;
    },
    onSuccess: (_data) => {
      toast.success('Persona created successfully!');
      queryClient.invalidateQueries({ queryKey: personaKeys.all });
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to create persona';
        toast.error(errorMessage);
      }
    },
  });
}

export function useUpdatePersonaMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: { id: string; persona: Partial<BasePersona> }) => {
      const response = await updatePersona(payload.id, payload.persona);
      return response.data;
    },
    onSuccess: (_data) => {
      toast.success('Persona updated successfully!');
      queryClient.invalidateQueries({ queryKey: personaKeys.all });
    },
    onError: (error: Error) => {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to update persona';
        toast.error(errorMessage);
      }
    },
  });
}
