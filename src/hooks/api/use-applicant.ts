import {
  getApplicants,
  getApplicantDetails,
  previewApplicantCV,
  deleteApplicant,
  exportApplicants,
} from '@/services/applicant';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { toast } from 'sonner';

export const applicantKeys = {
  all: ['applicants'] as const,
  lists: (params?: GetApplicantsParams) =>
    params
      ? ([...applicantKeys.all, 'list', params] as const)
      : ([...applicantKeys.all, 'list'] as const),
  list: (filters: string) => [...applicantKeys.lists(), { filters }] as const,
  details: () => [...applicantKeys.all, 'detail'] as const,
  detail: (id: string) => [...applicantKeys.details(), id] as const,
};

export const useApplicantsQuery = (params: GetApplicantsParams, options = { enabled: true }) => {
  return useQuery({
    queryKey: applicantKeys.lists(params),
    queryFn: async () => {
      const response = await getApplicants(params);
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000,
  });
};

export const useApplicantDetailsQuery = (applicantId: string, options = { enabled: true }) => {
  return useQuery({
    queryKey: applicantKeys.detail(applicantId),
    queryFn: async () => {
      const response = await getApplicantDetails(applicantId);
      return response.data;
    },
    enabled: options.enabled && !!applicantId,
    staleTime: 5 * 60 * 1000,
  });
};

export const usePreviewApplicantCVQuery = (cvLink: string, options = { enabled: true }) => {
  return useQuery({
    queryKey: ['preview-cv', cvLink],
    queryFn: async () => {
      const response = await previewApplicantCV(cvLink);
      return response.data;
    },
    enabled: options.enabled && !!cvLink,
    staleTime: 5 * 60 * 1000,
  });
};

export const useDeleteApplicantMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (applicantId: string) => deleteApplicant(applicantId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: applicantKeys.all });
      toast.success('Applicant deleted successfully');
    },
    onError: () => {
      toast.error('Failed to delete applicant. Please try again.');
    },
  });
};

export const useExportApplicantsMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: GetApplicantsParams) => exportApplicants(params),
    onSuccess: (data) => {
      const now = new Date();
      const timestamp = format(now, "yyyyMMdd'T'HHmmss");
      const filename = `previa_applicants_${timestamp}.xlsx`;

      const url = window.URL.createObjectURL(data.data);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      queryClient.invalidateQueries({ queryKey: applicantKeys.all });
      toast.success('Exported successfully');
    },
    onError: () => {
      toast.error('Failed to export applicants. Please try again.');
    },
  });
};
