import {
  attachRolesToUser,
  deleteUser,
  createUser,
  getAllUsers,
  detachRolesToUser,
} from '@/services/users';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export const usersKeys = {
  all: ['users'] as const,
  lists: (params?: UsersGetParams) =>
    [...usersKeys.all, 'list', ...(params ? [params] : [])] as const,
  list: (filters: string) => [...usersKeys.lists(), { filters }] as const,
};

export function useUsersQuery(params: UsersGetParams, options = { enabled: true }) {
  return useQuery({
    queryKey: usersKeys.lists(params),
    queryFn: async () => {
      const response = await getAllUsers(params);
      return response.data;
    },
    enabled: options.enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateUserMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['create-user'],
    mutationFn: (payload: UserCreatePayload) => {
      return createUser(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: usersKeys.lists() });
    },
    onError: (err) => {
      if (err.message) toast.error(err.message);
      toast.error('Failed to create user. Please try again.');
    },
  });
}

export function useDeleteUserMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['delete-user'],
    mutationFn: (userId: string) => deleteUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: usersKeys.lists() });
      toast.success('User deleted successfully');
    },
    onError: () => {
      toast.error('Failed to delete user. Please try again.');
    },
  });
}

export function useAttachRolesToUserMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['attach-roles-to-user'],
    mutationFn: (payload: AttachUserRolePayload) => {
      return attachRolesToUser(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: usersKeys.lists() });
      toast.success('Roles attached to user successfully');
    },
    onError: () => {
      toast.error('Failed to attach roles to user. Please try again.');
    },
  });
}

export function useDetachRolesToUserMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['detach-roles-to-user'],
    mutationFn: (payload: AttachUserRolePayload) => {
      return detachRolesToUser(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: usersKeys.lists() });
    },
    onError: () => {
      toast.error('Failed to attach roles to user. Please try again.');
    },
  });
}
