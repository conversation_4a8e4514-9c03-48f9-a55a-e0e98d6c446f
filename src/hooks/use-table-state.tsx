/* eslint-disable @typescript-eslint/no-explicit-any */
import { useDebounce } from './use-debounce';
import { usePagination } from './use-pagination';
import { useEffect, useMemo, useState } from 'react';

interface UseTableStateConfig {
  initialSearch?: string;
  initialFilters?: Record<string, any>;
  initialPageSize?: number;
  onSearchChange?: (search: string) => void;
  onFilterChange?: (key: string, value: any) => void;
  totalItems?: number;
}

export interface TablePaginationState {
  pageIndex: number;
  pageSize: number;
  isFirstPage: boolean;
  isLastPage: boolean;
  currentPageNumber: number;
  setPageIndex: (pageIndex: number) => void;
  setPageSize: (pageSize: number) => void;
  changePageSize: (pageSize: number) => void;
  goToPage: (pageNumber: number) => void;
  goToNextPage: () => void;
  goToPreviousPage: () => void;
  offset: number;
}

interface UseTableStateReturn {
  // State
  search: string;
  debouncedSearch: string;
  filters: Record<string, any>;
  queryParams: Record<string, any>;

  pagination: TablePaginationState;

  // Actions
  setSearch: (value: string) => void;
  updateFilter: (key: string, value: any) => void;
  clearFilter: (key: string) => void;
  clearAllFilters: () => void;
  reset: () => void;
}

export const useTableState = (config: UseTableStateConfig = {}): UseTableStateReturn => {
  const {
    initialSearch = '',
    initialFilters = {},
    initialPageSize = 10,
    onSearchChange,
    onFilterChange,
    totalItems = 0,
  } = config;

  // State
  const [search, setSearchState] = useState(initialSearch);
  const [filters, setFilters] = useState<Record<string, any>>(initialFilters);

  const debouncedSearch = useDebounce(search, 500);

  // Integrate usePagination
  const {
    pageIndex,
    pageSize,
    isFirstPage,
    isLastPage,
    currentPageNumber,
    setPageIndex,
    setPageSize,
    changePageSize,
    goToPage,
    goToNextPage,
    goToPreviousPage,
  } = usePagination({
    pageSize: initialPageSize,
    totalItems,
    oneBasedUrl: true,
  });

  // Calculate offset
  const offset = useMemo(() => (currentPageNumber - 1) * pageSize, [currentPageNumber, pageSize]);

  useEffect(() => {
    if (debouncedSearch !== initialSearch) {
      setPageIndex(0);
    }
    onSearchChange?.(debouncedSearch);
  }, [debouncedSearch, onSearchChange, initialSearch, setPageIndex]);

  const queryParams = useMemo(() => {
    const params: Record<string, any> = {
      page: currentPageNumber,
      limit: pageSize,
      pageSize: pageSize,
      offset,
    };

    if (debouncedSearch.trim()) {
      params.search = debouncedSearch.trim();
    }

    // Add all non-empty filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '' && value !== 'all') {
        params[key] = value;
      }
    });

    return params;
  }, [debouncedSearch, filters, currentPageNumber, pageSize, offset]);

  const setSearch = (value: string) => {
    setSearchState(value);
  };

  const updateFilter = (key: string, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPageIndex(0);
    onFilterChange?.(key, value);
  };

  const clearFilter = (key: string) => {
    setFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
    setPageIndex(0);
  };

  const clearAllFilters = () => {
    setFilters({});
    setPageIndex(0);
  };

  const reset = () => {
    setSearchState(initialSearch);
    setFilters(initialFilters);
    setPageIndex(0);
    setPageSize(initialPageSize);
  };

  return {
    // State
    search,
    debouncedSearch,
    filters,
    queryParams,

    // Pagination State and Actions
    pagination: {
      pageIndex,
      pageSize,
      isFirstPage,
      isLastPage,
      currentPageNumber,
      setPageIndex,
      setPageSize,
      changePageSize,
      goToPage,
      goToNextPage,
      goToPreviousPage,
      offset,
    },

    // Actions
    setSearch,
    updateFilter,
    clearFilter,
    clearAllFilters,
    reset,
  };
};

export type { UseTableStateConfig, UseTableStateReturn };
