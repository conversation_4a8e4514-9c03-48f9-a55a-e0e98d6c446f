import { useEffect } from 'react';

const useLockBodyScroll = (isLocked = false) => {
  useEffect(() => {
    const originalStyle = window.getComputedStyle(document.body).overflow;

    if (isLocked) {
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
      const currentScrollPosition = window.scrollY || document.documentElement.scrollTop;

      // Prevent content shift when scrollbar disappears
      document.body.style.paddingRight = `${scrollbarWidth}px`;

      // Lock the scroll and maintain position
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.top = `-${currentScrollPosition}px`;
      document.body.style.width = '100%';
    }

    return () => {
      if (isLocked) {
        // Restore original scroll position
        const scrollY = document.body.style.top;
        document.body.style.overflow = originalStyle;
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.paddingRight = '';
        window.scrollTo(0, parseInt(scrollY || '0', 10) * -1);
      }
    };
  }, [isLocked]);
};

export default useLockBodyScroll;
