'use client';

import { useEffect, useState } from 'react';

const ALLOWED_TIME_LIMIT = 5000;

const useTabVisibility = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [warningCount, setWarningCount] = useState(0);
  const [hasWarning, setHasWarning] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && hasInteracted) {
        const id = setTimeout(() => {
          setWarningCount((prevCount) => prevCount + 1);
          setHasWarning(true);
        }, ALLOWED_TIME_LIMIT);

        setTimeoutId(id);
        setIsVisible(false);
      } else {
        setIsVisible(true);
        setHasWarning(false);

        if (timeoutId) {
          clearTimeout(timeoutId);
          setTimeoutId(null);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [hasInteracted, timeoutId, warningCount]);

  // Checking if user has interacted with the page
  useEffect(() => {
    const handleInteraction = () => {
      setHasInteracted(true);
    };

    window.addEventListener('click', handleInteraction);
    window.addEventListener('keydown', handleInteraction);

    return () => {
      window.removeEventListener('click', handleInteraction);
      window.removeEventListener('keydown', handleInteraction);
    };
  }, []);

  return { isVisible, warningCount, hasWarning };
};

export default useTabVisibility;
