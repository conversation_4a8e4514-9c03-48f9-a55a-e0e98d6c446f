import { createDashboardSlice } from './slices/dashboard-slice';
import { PersonaSlice } from './slices/persona-slice';
import { createAuthSlice } from '@/stores/slices/auth-slice';
import { createJobSlice } from '@/stores/slices/job-slice';
import { createLanguageSlice } from '@/stores/slices/languag-slice';
import { createPersonaSlice } from '@/stores/slices/persona-slice';
import { createThemeSlice } from '@/stores/slices/theme-slice';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export type Store = AuthSlice &
  ThemeSlice &
  LanguageSlice &
  DashboardSlice &
  JobSlice &
  PersonaSlice;

export const useStore = create<Store>()(
  devtools(
    immer((...a) => ({
      ...createAuthSlice(...a),
      ...createThemeSlice(...a),
      ...createLanguageSlice(...a),
      ...createDashboardSlice(...a),
      ...createJobSlice(...a),
      ...createPersonaSlice(...a),
    })),
    {
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);
