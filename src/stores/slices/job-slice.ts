import { type StateCreator } from 'zustand';

const initialFormData: JobFormData = {
  jobTitle: '',
  title: '',
  positionAppliedFor: '',
  location: '',
  min_exp: '',
  max_exp: '',
  // skills: [],
  job_role_id: '',
  application_start_date: '',
  application_end_date: '',
  initial_filter_criteria: '',
  description: '',
  interviewPersonas: [],
  totalPersonas: 0,
  totalDuration: '0h',
};

const initialState: JobState = {
  currentStep: 1,
  formData: initialFormData,
};

export const createJobSlice: StateCreator<
  Store,
  [['zustand/immer', never], ['zustand/devtools', never]],
  [],
  JobSlice
> = (set, get) => ({
  ...initialState,
  setCurrentStep: (step: number) => set(() => ({ currentStep: step })),
  updateFormData: (data: Partial<JobFormData>) =>
    set((state: JobSlice) => ({
      formData: { ...state.formData, ...data },
    })),
  nextStep: () =>
    set(() => ({
      currentStep: Math.min(get().currentStep + 1, 3),
    })),
  prevStep: () =>
    set(() => ({
      currentStep: Math.max(get().currentStep - 1, 1),
    })),
  resetForm: () => set(() => ({ ...initialState })),
});
