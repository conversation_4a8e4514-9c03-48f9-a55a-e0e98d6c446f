import { Store } from '../store';
import { LLM_SERVICE } from '@/utils/constants';
import { StateCreator } from 'zustand';

export enum PersonaCreationMode {
  GUIDED = 'guided',
  MANUAL = 'manual',
}

const initialPersonaFormData: PersonaFormData = {
  id: '',
  title: '',
  description: '',
  prompt_text: '',
  prompt_generation_metadata: {},
};

const initialPersonaState = {
  mode: PersonaCreationMode.GUIDED,
  configuration: {
    llm: LLM_SERVICE.ANTHROPIC,
  },
  cvInfo: {
    id: '',
    filename: '',
    url: '',
  },
  personaFormData: initialPersonaFormData,
};

export interface PersonaSlice {
  mode: PersonaCreationMode;
  configuration: {
    llm: LLM_SERVICE;
  };
  cvInfo: {
    id: string;
    filename: string;
    url: string;
  };
  setMode: (mode: PersonaCreationMode) => void;
  setConfiguration: (config: PersonaSlice['configuration']) => void;
  setCvInfo: (data: Partial<PersonaSlice['cvInfo']>) => void;
  personaFormData: PersonaFormData;
  updatePersonaFormData: (data: Partial<PersonaFormData>) => void;
  resetPersonaForm: () => void;
}

export const createPersonaSlice: StateCreator<
  Store,
  [['zustand/immer', never], ['zustand/devtools', never]],
  [],
  PersonaSlice
> = (set) => ({
  ...initialPersonaState,
  setMode: (mode: PersonaCreationMode) => set({ mode }),
  setConfiguration: (config: PersonaSlice['configuration']) => set({ configuration: config }),
  setCvInfo: (data: Partial<PersonaSlice['cvInfo']>) =>
    set((state) => ({
      cvInfo: { ...state.cvInfo, ...data },
    })),
  updatePersonaFormData: (data: Partial<PersonaFormData>) =>
    set((state) => ({
      personaFormData: { ...state.personaFormData, ...data },
    })),
  resetPersonaForm: () =>
    set(() => ({
      ...initialPersonaState,
      mode: PersonaCreationMode.GUIDED,
      configuration: {
        llm: LLM_SERVICE.ANTHROPIC,
      },
      cvInfo: {
        id: '',
        filename: '',
        url: '',
      },
    })),
});
