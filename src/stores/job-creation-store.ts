import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface JobCreationState {
  draftJobId: string | null;
  setDraftJobId: (id: string | null) => void;
  clearDraftJobId: () => void;
}

export const useJobCreationStore = create<JobCreationState>()(
  persist(
    (set) => ({
      draftJobId: null,

      setDraftJobId: (id: string | null) => set({ draftJobId: id }),
      clearDraftJobId: () => set({ draftJobId: null }),
    }),
    {
      name: 'job-creation-storage',
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
