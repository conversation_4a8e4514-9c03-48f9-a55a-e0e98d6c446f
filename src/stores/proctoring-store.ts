import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface ProctoringState {
  logViolations: ViolationLogger[];
  addViolation: (violation: ViolationLogger) => void;
  clearViolations: () => void;
  totalViolations: number;
}

export const useProctoringStore = create<ProctoringState>()(
  // persist(
  (set) => ({
    logViolations: [],
    totalViolations: 0,
    addViolation: (violation: ViolationLogger) => {
      set((state) => ({
        logViolations: [...state.logViolations, violation],
        totalViolations: state.totalViolations + 1,
      }));
    },
    clearViolations: () => {
      set({ logViolations: [] });
    },
  })
  // {
  //   name: 'proctoring-storage',
  //   storage: createJSONStorage(() => sessionStorage),

  // }
  // )
);
