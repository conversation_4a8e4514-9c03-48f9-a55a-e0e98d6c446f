export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE_INDEX = 0;
export const DEFAULT_PAGE_NUMBER = 1;
export const MAX_PAGE_SIZE = 100;

export enum JOB_STATUS {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
}

export enum ErrorCode {
  NOT_FOUND = 'not_found',
  UNAUTHORIZED = 'unauthorized',
  FORBIDDEN = 'forbidden',
  BAD_REQUEST = 'bad_request',
  INTERNAL_SERVER_ERROR = 'internal_server_error',
}

export enum JOB_CANDIDATE_STATUS {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  INCOMPLETE = 'incomplete',
  COMPLETED = 'completed',
  NOT_INTERVIEWED = 'not_interviewed',
}

export enum LLM_SERVICE {
  GEMINI = 'gemini',
  ANTHROPIC = 'anthropic',
  GROQ = 'groq',
}
