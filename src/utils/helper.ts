/* eslint-disable @typescript-eslint/no-explicit-any */
import { JOB_STATUS } from './constants';
import { JOB_CANDIDATE_STATUS } from '@/utils/constants';

export function getCapitalizedText(text: string): string {
  if (!text) return text;
  return text?.charAt(0)?.toUpperCase() + text?.slice(1).toLowerCase();
}

export const getValuesFromOptions = (options: SelectProp[]) => {
  if (!options || options.length === 0) return [];

  return options.map((option: SelectProp) => option.value);
};

export const getDateToISOString = (date: Date | undefined): string | undefined => {
  if (!date) return undefined;

  const normalizedDate = new Date(date);
  normalizedDate.setHours(0, 0, 0, 0); // Set time to midnight (local time)

  // Adjust for local timezone offset
  const timezoneOffset = normalizedDate.getTimezoneOffset(); // Offset in minutes
  normalizedDate.setMinutes(normalizedDate.getMinutes() - timezoneOffset);

  return normalizedDate.toISOString(); // Convert to ISO string
};

export function getInitials(name: string, maxLength: number = 2, fallback: string = 'AP'): string {
  // Handle null, undefined, or empty string
  if (!name || typeof name !== 'string') {
    return fallback;
  }

  // Clean the name: trim whitespace and remove extra spaces
  const cleanName = name.trim().replace(/\s+/g, ' ');

  if (!cleanName) {
    return fallback;
  }

  // Split by spaces and filter out empty strings
  const words = cleanName.split(' ').filter((word) => word.length > 0);

  if (words.length === 0) {
    return fallback;
  }

  // Generate initials from each word
  const initials = words
    ?.slice(0, maxLength) // Limit to maxLength words
    ?.map((word) => {
      // Get first character that is a letter
      const firstLetter = word.match(/[a-zA-Z]/);
      return firstLetter ? firstLetter[0].toUpperCase() : '';
    })
    ?.filter((initial) => initial.length > 0) // Remove empty initials
    ?.join('');

  // If no valid initials found, return fallback
  if (!initials) {
    return fallback;
  }

  return initials?.slice(0, maxLength);
}

export const fileSizeValidation = (files: File[], maxFileSize: number) => {
  const MAX_FILE_SIZE_BYTES = maxFileSize * 1024 * 1024;
  return files.every((file) => file.size <= MAX_FILE_SIZE_BYTES);
};

export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function formatInterviewDuration(totalMinutes: number): string {
  if (totalMinutes < 60) {
    return `${totalMinutes}m`;
  }

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  if (minutes === 0) {
    return `${hours}h`;
  }

  return `${hours}h ${minutes}m`;
}

export function getStatusBadgeVariant(status: `${JOB_STATUS}`) {
  switch (status) {
    case JOB_STATUS.DRAFT:
      return 'secondary';
    case JOB_STATUS.ACTIVE:
      return 'success';
    case JOB_STATUS.INACTIVE:
      return 'destructive';
    default:
      return 'secondary';
  }
}

export function getCandidateBadgeVariant(status: `${JOB_CANDIDATE_STATUS}`) {
  switch (status) {
    case JOB_CANDIDATE_STATUS.COMPLETED:
      return 'success';
    case JOB_CANDIDATE_STATUS.IN_PROGRESS:
      return 'info';
    case JOB_CANDIDATE_STATUS.INCOMPLETE:
      return 'destructive';
    case JOB_CANDIDATE_STATUS.PENDING:
      return 'info';
    case JOB_CANDIDATE_STATUS.NOT_INTERVIEWED:
      return 'secondary';
    default:
      return 'secondary';
  }
}

export const getUserRoleBadgeColor = (role: string) => {
  switch (role) {
    case 'super_admin':
      return 'destructive';
    case 'admin':
      return 'warning';
    case 'user':
      return 'success';
    default:
      return 'info';
  }
};

export function snakeToTitleCase(str: string): string {
  return str
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

export function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });
  const qs = searchParams.toString();
  return qs ? `?${qs}` : '';
}

export const buildQueryParams = (params: Record<string, any>) => {
  return Object.fromEntries(
    Object.entries(params).filter(
      ([_, value]) => value !== '' && value !== null && value !== undefined
    )
  );
};

export function getTimeLeft(endDate?: string): string {
  if (!endDate) return '-';
  const end = new Date(endDate);
  if (isNaN(end.getTime())) return '-';
  const now = new Date();
  const diff = end.getTime() - now.getTime();
  if (diff <= 0) return 'Closed';
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  return days === 0 ? 'Last day' : `${days} day${days > 1 ? 's' : ''}`;
}
