import { SidebarIconProps } from '@/types/sidebar';
import React from 'react';

export const AddressBookIcon: React.FC<SidebarIconProps> = ({ variant = 'default', ...props }) => {
  const isActive = variant === 'active';

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      {...props}
    >
      <g clipPath='url(#clip0_1137_26650)'>
        <path
          opacity={isActive ? '0.5' : '1'}
          d='M19.5 3H6C5.80109 3 5.61032 3.07902 5.46967 3.21967C5.32902 3.36032 5.25 3.55109 5.25 3.75V20.25C5.25 20.4489 5.32902 20.6397 5.46967 20.7803C5.61032 20.921 5.80109 21 6 21H19.5C19.6989 21 19.8897 20.921 20.0303 20.7803C20.171 20.6397 20.25 20.4489 20.25 20.25V3.75C20.25 3.55109 20.171 3.36032 20.0303 3.21967C19.8897 3.07902 19.6989 3 19.5 3ZM12.75 13.5C12.1567 13.5 11.5766 13.3241 11.0833 12.9944C10.5899 12.6648 10.2054 12.1962 9.97836 11.6481C9.7513 11.0999 9.69189 10.4967 9.80764 9.91473C9.9234 9.33279 10.2091 8.79824 10.6287 8.37868C11.0482 7.95912 11.5828 7.6734 12.1647 7.55764C12.7467 7.44189 13.3499 7.5013 13.8981 7.72836C14.4462 7.95542 14.9148 8.33994 15.2444 8.83329C15.5741 9.32664 15.75 9.90666 15.75 10.5C15.75 11.2956 15.4339 12.0587 14.8713 12.6213C14.3087 13.1839 13.5456 13.5 12.75 13.5Z'
          fill={isActive ? '#5C92FA' : 'none'}
        />
        <path
          d='M12.75 13.5C14.4069 13.5 15.75 12.1569 15.75 10.5C15.75 8.84315 14.4069 7.5 12.75 7.5C11.0931 7.5 9.75 8.84315 9.75 10.5C9.75 12.1569 11.0931 13.5 12.75 13.5Z'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M3 6.75H5.25'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M3 12H5.25'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M3 17.25H5.25'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M8.25 15.75C8.77395 15.0514 9.45336 14.4844 10.2344 14.0938C11.0155 13.7033 11.8767 13.5 12.75 13.5C13.6233 13.5 14.4845 13.7033 15.2656 14.0938C16.0466 14.4844 16.726 15.0514 17.25 15.75'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M20.25 20.25V3.75C20.25 3.33579 19.9142 3 19.5 3L6 3C5.58579 3 5.25 3.33579 5.25 3.75L5.25 20.25C5.25 20.6642 5.58579 21 6 21H19.5C19.9142 21 20.25 20.6642 20.25 20.25Z'
          stroke='currentColor'
          strokeWidth='1.5'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_1137_26650'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};
