import animationData from '../../../src/assets/lottie/document-loader.json';
import Lot<PERSON> from 'react-lottie';

const QuestionGeneratingAnimation = () => {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidY;;Mid slice',
    },
  };
  return (
    <div>
      <Lottie options={defaultOptions} width={175} height={175} />
    </div>
  );
};

export default QuestionGeneratingAnimation;
