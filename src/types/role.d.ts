declare global {
  interface Role {
    ID?: number;
    Name: string;
    Description: string;
  }

  type RolePartial = Partial<Role>;

  interface Roles {
    roles: Role[];
  }

  interface RoleRecord {
    Role: Role;
    Permissions: RolePermissionAttachment[];
  }

  interface RoleResponse {
    message: string;
    data: {
      records: RoleRecord[];
      total: number;
      sort: Sort;
      page: Page;
    };
  }

  interface RolesParams {
    number?: number;
    limit?: number;
  }

  interface RoleSavePayload{
    name: string;
    description: string;
  }
}

export {};
