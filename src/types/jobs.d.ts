import { JobStatus } from '../features/admin/jobs/job-list/utils/schema';
import { JOB_STATUS } from '@/utils/constants';

declare global {
  interface GetJobsParams {
    limit?: number;
    offset?: number;
    status?: JobStatus;
    search?: string;
  }
  interface JobFormData {
    jobTitle: string;
    title: string;
    positionAppliedFor: string;
    location: string;
    min_exp: string | number;
    max_exp: string | number;
    // skills?: string[];
    job_role_id?: string;
    application_start_date: string | undefinded;
    application_end_date: string | undefinded;
    initial_filter_criteria?: string | null;
    description?: string;
    interviewPersonas: InterviewPersona[];
    totalPersonas: number;
    totalDuration: string;
  }

  interface InterviewPersona {
    id: string;
    persona: string;
    duration: string;
  }
  interface JobState {
    currentStep: number;
    formData: JobFormData;
  }

  interface JobActions {
    setCurrentStep: (step: number) => void;
    updateFormData: (data: Partial<JobFormData>) => void;
    nextStep: () => void;
    prevStep: () => void;
    resetForm: () => void;
  }

  type JobSlice = JobState & JobActions;

  export interface JobInformation {
    title: string;
    description?: string;
    job_role_id: string;
    location: string;
    min_exp: number;
    max_exp: number;
    // skills?: string[];
    application_start_date: string | undefined;
    application_end_date: string | undefined;
    initial_filter_criteria?: string | null;
    status: JobStatus;
    has_persona?: boolean;
    has_description?: boolean;
    job_role?: {
      name: string;
      description?: string;
      id: string;
    };
  }

  interface JobInformationResponse extends JobInformation {
    id: string;
  }

  type PreviewJob = Partial<JobInformation>;

  export type JobDetailsAdmin = {
    id: string;
    title: string;
    description: string;
    location: string;
    min_exp: number;
    max_exp: number;
    status: `${JOB_STATUS}`;
    application_start_date: string;
    application_end_date: string;
    job_role: {
      id: string;
      name: string;
      description: string;
    };
    initial_filter_criteria: string;
    has_description: boolean;
    has_persona: boolean;
    candidate_count: {
      total: number;
      eligible: number;
      attended: number;
    };
    interview_duration: number;
    created_at: string;
    updated_at: string;
  };

  interface JobList {
    id: string;
    title: string;
    location?: string;
    min_exp?: number;
    max_exp?: number;
    status: JobStatus;
    application_end_date?: string;
    candidate_count?: { total: number; eligible: number; attended: number };
  }

  interface JobListResponse extends JobList {
    items: JobList[];
    limit?: number;
    offset?: number;
    total: number;
  }

  interface ActiveJob {
    id: string;
    title: string;
    location: string;
    min_exp: number;
    max_exp: number;
  }

  interface JobRole {
    id: string;
    name: string;
    description?: string;
  }

  interface JobRolePayload {
    name: string;
    description?: string;
  }

  export type JobCandidate = {
    id: string;
    full_name: string;
    email: string;
    is_eligible_for_interview: boolean;
    interview_status: string;
    interview_date: string;
  };

  export interface JobCandidateResponse {
    total: string;
    limit: string;
    offset: string;
    items: JobCandidate[];
  }
}

export {};
