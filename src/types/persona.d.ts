interface Persona {
  id: string;
  title: string;
  description: string;
  prompt_text: string;
  prompt_generation_metadata: Record<string, string | string[]>;
  created_at: string;
  updated_at: string;
}

interface PersonaFormData {
  id: string;
  title: string;
  description: string;
  prompt_text: string;
  prompt_generation_metadata: Record<string, string | string[]>;
}

interface BasePersona {
  title: string;
  description: string;
  prompt_text: string;

  prompt_generation_metadata: Record<string, string | string[]>;
}

interface ExtendedPersona extends BasePersona {
  id: string;
  created_by: string;
  created_at: string;
  attached_job_count: string;
}

interface JobPersona {
  persona_id: string;
  time_duration: number;
  execution_order: number;
}

interface AttachJobPersona {
  id: string;
  job_id: string;
  persona_title: string;
  persona_id: string;
  time_duration: number;
  execution_order: number;
}

interface PromptGenerationPayload {
  role: string;
  expectations: string;
  keywords: string[];
  difficulty: string;
}

interface PromptGenerateResponse {
  prompt_text: string;
  description: string;
}

interface PersonaAttachedJobs {
  id: string;
  title: string;
  location: string;
  min_exp: number;
  max_exp: number;
  application_start_date: string;
  application_end_date: string;
}

interface PersonaCreatePayload {
  title: string;
  description: string;
  prompt_text: string;
  prompt_generation_metadata: Record<string, string | string[]>;
  created_by: string;
}
