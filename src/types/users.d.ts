declare global {
  interface User {
    id: string;
    user_name: string;
    email: string;
    is_active?: boolean;
    permissions?: UserPermissions[];
    roles?: UserRoles[];
  }

  type UserPartial = Partial<User>;

  interface Users {
    users: User[];
  }

  interface UserListResponse {
    data: {
      records: User[];
      total: number;
      page: {
        number: number;
        limit: number;
      };
    };
  }

  interface UsersGetParams {
    search?: string;
    number?: number;
    limit?: number;
    sort?: {
      by: string;
      direction: string;
    };
  }

  interface UserRoles {
    AttachmentID: number;
    RoleID: number;
    RoleName: string;
  }

  interface UserPermissions {
    AttachmentID: number;
    PermissionID: number | string;
    PermissionName: string;
  }

  export interface UserCreatePayload {
    id?: string;
    user_name?: string;
    name?: string;
    email: string;
    password: string;
  }

  export interface AttachUserRolePayload {
    user_id: User['id'];
    role_ids: number[];
  }
}

export {};
