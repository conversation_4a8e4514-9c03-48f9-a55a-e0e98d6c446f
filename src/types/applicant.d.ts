import { JOB_CANDIDATE_STATUS } from '@/utils/constants';

declare global {
  interface GetApplicantsParams {
    limit?: number;
    offset?: number;
    search?: string;
    interview_status?: string;
  }

  interface ListingParams {
    limit?: number;
    offset?: number;
    search?: string;
  }

  export type Applicant = {
    id: string;
    full_name: string;
    email: string;
    job_title: string;
    job_id: string;
    interview_status: string;
    interview_date: string;
  };
  export interface ApplicantsResponse {
    items: Applicant[];
    total: number;
    limit: number;
    offset: number;
  }

  export type InterviewDetail = {
    id: string;
    status: `${JOB_CANDIDATE_STATUS}`;
    interview_date: string;
    qa_array: {
      question: string;
      answer: string;
      chat_id?: string;
    }[];
    evaluation: {
      id: string;
      interview_id: string;
      evaluation_data: string;
    };
  };

  export type ApplicantDetails = {
    id: string;
    full_name: string;
    email: string;
    cv_link: string;
    apply_date_time: string;
    years_of_experience: string;
    job: {
      id: string;
      title: string;
      location: string;
      min_exp: string;
      max_exp: string;
    };
    interviews: InterviewDetail[];
  };

  export type PreviewCVResponse = {
    url: string;
  };
}

export {};
