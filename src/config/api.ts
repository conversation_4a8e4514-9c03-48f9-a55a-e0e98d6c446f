export const API_ROUTES = {
  AUTH: {
    LOGIN: '/auth/login',
    REFRESH_TOKEN: '/auth/refresh-token',
  },

  USER: {
    PROFILE: '/users/profile',
    UPDATE_PROFILE: '/users/profile',
    CHANGE_PASSWORD: (id: string) => `/users/${id}/change-password`,
    GET_ALL: '/users/',
    CREATE: '/users/',
    ATTACH_ROLES: `/user-role/attachment/`,
    DETACH_ROLES: (userId: string) => `/user-role/attachment/${userId}`,
    DELETE: (id: string) => `/users/${id}`,
  },
  ROLE: {
    GET_ALL: '/roles/',
    CREATE: '/roles/',
    UPDATE: (id: number) => `/roles/${id}`,
    DELETE: (id: number) => `/roles/${id}`,
  },

  INTERVIEW: {
    CREATE_FLOW: '/api/v1/interview-configs',
    GET_ALL_FLOWS: (limit: number, offset: number) =>
      `/api/v1/interview-configs?limit=${limit}&offset=${offset}`,
    PREPARE_INTERVIEW: '/interviews/prepare',
    CREATE_USER_INTERVIEW: '/interviews',

    RUN_EVALUATION: (interviewId: string) => `/interview-evaluations/${interviewId}/generate`,
    COMPLETE_INTERVIEW: (interviewId: string) => `/interviews/${interviewId}/complete`,
    SUBMIT_FEEDBACK: (userInterviewId: string) => `/api/v1/interviews/${userInterviewId}/feedback`,
    GET_CANDIDATES_BY_JOB: (
      jobId: string,
      limit: number,
      offset: number,
      status: string | undefined,
      searchParam?: string
    ) =>
      `/api/v1/dashboards/jobs/${jobId}/candidates?limit=${limit}&offset=${offset}&status=${status}${searchParam}`,
    GET_CANDIDATE_EVALUATION: (jobId: string, candidateId: string) =>
      `/api/v1/dashboards/jobs/${jobId}/candidates/${candidateId}/evaluation`,
    GET_CANDIDATE_COUNT: (jobId: string) => `/api/v1/candidates/count/${jobId}`,
    GET_DASHBOARD_JOBS: '/api/v1/dashboards/jobs',
    RESEND_INVITATION: '/api/v1/interviews/resend-invitation',
    ADD_FEEDBACK: (interviewId: string) => `/interviews/${interviewId}/feedback`,
  },

  PROMPT: {
    PREPARE_LIVE_INTERVIEW: '/question-engine/prepare-interview',
    GENERATE: '/api/v1/agents/generate-prompt',
    LIVE_PREVIEW: '/live-interview/next-question',
    LIVE_EVALUATION: '/api/v1/live-evaluation',
    PREVIEW_CV: (cvLink: string) => `/api/v1/candidates/preview-cv?cv_link=${cvLink}`,
    UPLOAD_PREVIEW_CV: (personaId: string) => `/personas/${personaId}/preview/upload-cv`,
    GET_CV_URL: (personaId: string, cvId: string) =>
      `/personas/${personaId}/preview/cvs/${cvId}/presigned-url`,
    GET_PREVIEW_CVS: (personaId: string) => `/personas/${personaId}/preview/cvs`,
  },

  JOB: {
    CREATE: '/jobs',
    GET_ALL: '/jobs',
    GET_ACTIVE_JOBS: '/jobs/active',
    GET_JOB_ROLES: '/job-roles',
    CREATE_JOB_ROLE: '/job-roles',
    UPDATE_JOB_ROLE: (roleId: string) => `/job-roles/${roleId}`,
    DELETE_JOB_ROLE: (roleId: string) => `/job-roles/${roleId}`,
    GET_DETAILS: (jobId: string) => `/jobs/${jobId}/details`,
    GET_BY_ID: (jobId: string) => `/jobs/${jobId}`,
    GET_GENERATE_DESCRIPTION: (jobId: string) => `/jobs/${jobId}/generate-description`,
    GET_DETAILS_FOR_ADMIN: (jobId: string) => `/jobs/${jobId}`,
    LIST_CANDIDATE: (jobId: string) => `/jobs/${jobId}/candidates`,
    DELETE: (jobId: string) => `/jobs/${jobId}`,
  },

  CANDIDATE: {
    CREATE: '/candidates',
    VERIFY: '/candidates/verify',
    UPLOAD_CV: (jobId: string) => `/jobs/${jobId}/candidates/upload-cv`,
    APPLY_JOB: (jobId: string) => `/jobs/${jobId}/candidates`,
    VERIFY_EMAIL: '/candidates/verify-email',
    RESEND_OTP: '/candidates/resend-otp',
    COMPLETE_PROFILE: (candidateId: string) => `/candidates/${candidateId}/complete`,
  },
  APPLICANT: {
    GET_ALL: '/candidates',
    EXPORT: '/candidates/export',
    GET_DETAILS: (applicantId: string) => `/candidates/${applicantId}`,
    PREVIEW_CV: (cvLink: string) => `/candidates/preview-cv?cv_link=${cvLink}`,
    DELETE: (applicantId: string) => `/candidates/${applicantId}`,
  },

  STT: {
    TRANSCRIBE_SYNC: '/transcribe/sync',
  },
  QUESTION: {
    GENERATE: '/question-engine/generate-question/sync',
    GENERATE_SYNC: '/question-engine/generate-question/sync',
    UPDATE_ANSWER: '/question-engine/update-answer',
  },
  PERSONA: {
    GET_ALL: '/personas',
    ATTACH_TO_JOB: (jobId: string) => `/jobs/${jobId}/personas/batch`,
    GET_JOB_PERSONAS: (jobId: string) => `/jobs/${jobId}/personas`,
    GET_BY_ID: (personaId: string) => `/personas/${personaId}`,
    UPDATE: (personaId: string) => `/personas/${personaId}`,
    DELETE: (personaId: string) => `/personas/${personaId}`,
    GENERATE_PROMPT: '/personas/prompt/generate',
    GET_JOBS: (personaId: string) => `/personas/${personaId}/jobs`,
    CREATE: '/personas',
  },
};
