import { RoleActions } from '../components/role-actions';
import LongText from '@/components/long-text';
import { ColumnDef } from '@tanstack/react-table';

interface ColumnsProps {
  onEdit: (role: Role) => void;
  onDelete: (roleId: number) => Promise<void>;
}

export const createRoleColumns = ({ onEdit, onDelete }: ColumnsProps): ColumnDef<RoleRecord>[] => [
  {
    accessorKey: 'name',
    header: 'User Role',
    cell: ({ row }) => <div className='font-medium'>{row.original.Role.Name}</div>,
  },
  {
    accessorKey: 'description',
    header: 'Description',
    meta: {
      maxSize: '450',
    },
    cell: ({ row }) => {
      const description = row.original.Role.Description;
      return (
        <LongText className='text-muted-foreground max-w-md'>
          {description || 'No description provided'}
        </LongText>
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    meta: {
      align: 'right',
    },
    cell: ({ row }) => {
      const role = row.original.Role;
      return <RoleActions role={role} onEdit={onEdit} onDelete={onDelete} />;
    },
  },
];
