import ContentHeader from '../components/content-header';
import CreateJobRoleDialog from './components/create-role-dialog';
import { createRoleColumns } from './utils/columns';
import {
  DataTable,
  DataTableContainer,
  DataTableFooter,
  DataTableHeader,
} from '@/components/data-table';
import TablePagination from '@/components/shared/table-pagination';
import { Button } from '@/components/ui/button';
import { useDeleteRoleMutation, useRolesQuery, useUpdateRoleMutation } from '@/hooks/api/use-role';
import { useTableState } from '@/hooks/use-table-state';
import { Plus, ShieldUser } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { toast } from 'sonner';

export default function RoleSettings() {
  const { pagination } = useTableState();
  const { data: rolesData, isLoading } = useRolesQuery({
    limit: pagination.pageSize,
    number: pagination.pageIndex + 1,
  });
  const { mutateAsync: updateRole } = useUpdateRoleMutation();
  const { mutateAsync: deleteRole } = useDeleteRoleMutation();

  const handleEditRole = useCallback(
    (updatedRole: Role) => {
      if (!updatedRole.Name.trim()) {
        toast.error('Role name is required');
        return;
      } else if (!updatedRole.ID) {
        toast.error('Please try again later');
        return;
      }
      updateRole({
        roleId: updatedRole.ID,
        payload: {
          name: updatedRole.Name.trim(),
          description: updatedRole.Description?.trim() || '',
        },
      }).catch(() => {
        // error handled in hook
      });
    },
    [updateRole]
  );

  const handleDeleteRole = useCallback(
    async (roleId: number) => {
      try {
        await deleteRole(roleId);
      } catch (_e) {
        // error handled in hook
      }
    },
    [deleteRole]
  );

  const columns = useMemo(
    () =>
      createRoleColumns({
        onEdit: handleEditRole,
        onDelete: handleDeleteRole,
      }),
    [handleEditRole, handleDeleteRole]
  );

  return (
    <div className='h-full w-full p-8'>
      <ContentHeader
        title='Manage User Roles'
        description='Create, edit, and delete user roles that allow you to control access and permissions'
        icon={<ShieldUser className='size-6' />}
      />

      <div className='my-6'>
        <DataTableContainer>
          <DataTableHeader>
            <DataTableHeader.Left>
              <div className='flex flex-col items-start gap-2'>
                <h3 className='text-lg font-semibold'>User Roles</h3>
                <p className='text-muted-foreground text-sm'>
                  {rolesData?.total || 0} {rolesData?.total === 1 ? 'role' : 'roles'} configured
                </p>
              </div>
            </DataTableHeader.Left>
            <DataTableHeader.Right>
              <CreateJobRoleDialog
                trigger={
                  <Button size={'lg'}>
                    <Plus className='size-4' />
                    Add User Role
                  </Button>
                }
              />
            </DataTableHeader.Right>
          </DataTableHeader>

          {!isLoading && rolesData?.total === 0 ? (
            <div className='flex flex-col items-center justify-center py-12 text-center'>
              <h3 className='mb-2 text-lg font-semibold'>No job roles yet</h3>
              <p className='text-muted-foreground mb-4'>
                Get started by creating your first job role with "Add Role" button.
              </p>
            </div>
          ) : (
            <>
              <DataTable data={rolesData?.records || []} columns={columns} isLoading={isLoading} />
              <DataTableFooter>
                <TablePagination totalRecords={rolesData?.total || 0} />
              </DataTableFooter>
            </>
          )}
        </DataTableContainer>
      </div>
    </div>
  );
}
