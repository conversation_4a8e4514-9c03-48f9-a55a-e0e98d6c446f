import { SettingsSidebar } from './components/settings-sidebar';
import { Main } from '@/components/layout/main';
import { PageHeader } from '@/components/shared/PageHeader';
import { ScrollArea } from '@/components/ui';
import { Outlet } from '@tanstack/react-router';

export default function Settings() {
  return (
    <div className='bg-custom-white min-h-screen'>
      <PageHeader title='Settings' />

      <Main fixed className='pt-0'>
        <div className='flex h-[calc(100vh-2rem)] flex-1 flex-col overflow-hidden rounded-2xl lg:flex-row'>
          <div className='h-[calc(100vh-115px)] w-1/5 min-w-60'>
            <SettingsSidebar />
          </div>
          <ScrollArea className='flex h-[calc(100vh-115px)] w-full bg-white'>
            <Outlet />
          </ScrollArea>
        </div>
      </Main>
    </div>
  );
}
