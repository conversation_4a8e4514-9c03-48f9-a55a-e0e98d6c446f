'use client';

import { cn } from '@/libs/utils';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { useState } from 'react';

interface SidebarItem {
  id: string;
  title: string;
  href: string;
}

const sidebarItems: SidebarItem[] = [
  {
    id: 'job-role',
    title: 'Job Role',
    href: '/admin/settings',
  },
  {
    id: 'user-management',
    title: 'User Management',
    href: '/admin/settings/user-management',
  },
  {
    id: 'user-role',
    title: 'User Role',
    href: '/admin/settings/user-role',
  },
];

export function SettingsSidebar() {
  const navigate = useNavigate();

  const [activeSection, setActiveSection] = useState<SidebarItem['id']>('job-role');

  const onSectionChange = (section: SidebarItem) => {
    setActiveSection(section.id);
    navigate({ to: section.href });
  };

  return (
    <aside className='bg-sidebar flex h-full flex-col p-6 pr-0'>
      <nav className='flex-1 space-y-2 border-r border-gray-200/60 pr-6'>
        {sidebarItems.map((item) => (
          <motion.button
            key={item.id}
            onClick={() => onSectionChange(item)}
            className={cn(
              'relative w-full rounded-lg px-4 py-2.5 text-left text-sm font-medium transition-colors',
              activeSection === item.id
                ? 'bg-primary-50 text-primary'
                : 'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
            )}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            transition={{ duration: 0.2 }}
          >
            {activeSection === item.id && (
              <motion.div
                className='bg-primary-50 absolute inset-0 rounded-lg'
                layoutId='activeBackground'
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              />
            )}
            <span
              className={cn(
                'relative z-20',
                activeSection === item.id ? 'text-primary-500 font-semibold' : 'text-gray-dark'
              )}
            >
              {item.title}
            </span>
          </motion.button>
        ))}
      </nav>
    </aside>
  );
}
