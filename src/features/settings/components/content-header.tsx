import React from 'react';

interface Props {
  title: string;
  description: string;
  icon: React.ReactNode;
}

const ContentHeader = ({ title, description, icon }: Props) => {
  return (
    <div className='flex items-center gap-4'>
      <div className='from-primary-400 to-primary-500 rounded-xl bg-gradient-to-b p-3 text-white'>
        {icon}
      </div>
      <div>
        <p className='text-gray-dark mb-1 text-xl font-semibold'>{title}</p>
        <p className='text-gray-light text-sm'>{description}</p>
      </div>
    </div>
  );
};

export default ContentHeader;
