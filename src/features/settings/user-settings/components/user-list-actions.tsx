import { AttachRolesModal } from './attach-roles-modal';
import { ConfirmationModalInput } from '@/components/shared/confirmation-modal-input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  useAttachRolesToUserMutation,
  useDeleteUserMutation,
  useDetachRolesToUserMutation,
} from '@/hooks/api/use-user';
import { AxiosError } from 'axios';
import { Ellipsis, ShieldUser } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface UserListActionsProps {
  user: User;
}
export function UserListActions({ user }: UserListActionsProps) {
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
  const [isAttachRolesModalOpen, setIsAttachRolesModalOpen] = useState(false);

  const { mutate: attachRoles } = useAttachRolesToUserMutation();
  const { mutate: detachRoles } = useDetachRolesToUserMutation();
  const { mutate: deleteUser } = useDeleteUserMutation();

  // const handleRemoveClick = () => {
  //   setIsDeleteConfirmModalOpen(true);
  // };

  const handleDeleteUser = async () => {
    try {
      await deleteUser(user.id);
      setIsDeleteConfirmModalOpen(false);
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorMessage = error.response?.data?.message || 'Failed to delete user';
        toast.error(errorMessage);
      }
    }
  };

  const handleAttachRoles = () => {
    setIsAttachRolesModalOpen(true);
  };

  const handleSaveRoles = (roleIds: number[]) => {
    const prevRoleIds = user.roles?.map((r) => r.RoleID) ?? [];
    const added = roleIds.filter((id) => !prevRoleIds.includes(id));
    const removed = prevRoleIds.filter((id) => !roleIds.includes(id));

    if (added.length) {
      attachRoles({ user_id: user.id, role_ids: added });
    }
    if (removed.length) {
      detachRoles({ user_id: user.id, role_ids: removed });
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className='rounded-sm p-1 hover:bg-neutral-200'>
            <Ellipsis className='size-4 cursor-pointer' />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='font-manrope min-w-[120px]'>
          <DropdownMenuItem onClick={handleAttachRoles}>
            <ShieldUser className='mr-2 size-4' /> Attach Roles
          </DropdownMenuItem>

          {/* <DropdownMenuItem
            onClick={handleRemoveClick}
            className='text-destructive hover:bg-destructive/10 focus:bg-destructive/10'
          >
            <Trash2 className='text-destructive mr-2 size-4' /> Remove User
          </DropdownMenuItem> */}
        </DropdownMenuContent>
      </DropdownMenu>

      <AttachRolesModal
        userId={user.id}
        userName={user.user_name}
        userRoles={user.roles}
        isOpen={isAttachRolesModalOpen}
        onClose={setIsAttachRolesModalOpen}
        onSave={handleSaveRoles}
      />

      <ConfirmationModalInput
        title={`Remove User`}
        description={`Are you sure you want to remove ${user.user_name} user? All data related to this account will be permanently deleted.`}
        onConfirm={handleDeleteUser}
        open={isDeleteConfirmModalOpen}
        onOpenChange={setIsDeleteConfirmModalOpen}
        confirmationText='DELETE'
        confirmationPlaceholder='Enter DELETE to confirm deletion'
        showWarning
      />
    </>
  );
}
