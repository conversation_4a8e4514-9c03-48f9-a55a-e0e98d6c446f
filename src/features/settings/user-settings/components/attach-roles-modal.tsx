import { MultiSelect } from '@/components/select/multiple-selector';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui';
import { useRolesQuery } from '@/hooks/api/use-role';
import { useState, useEffect } from 'react';

interface AttachRolesModalProps {
  userId?: string;
  userName: string;
  userRoles?: UserRoles[];
  isOpen: boolean;
  onClose: (open: boolean) => void;
  onSave: (selectedRoles: number[]) => void;
}

interface RoleOption {
  value: string;
  label: string;
}

export function AttachRolesModal({
  userName,
  isOpen,
  onClose,
  onSave,
  userRoles = [],
}: AttachRolesModalProps) {
  const { data: rolesData, isLoading } = useRolesQuery();
  const [selectedRoles, setSelectedRoles] = useState<RoleOption[]>([]);
  const [roleOptions, setRoleOptions] = useState<RoleOption[]>([]);

  useEffect(() => {
    if (!rolesData) return;

    const options: RoleOption[] = rolesData.records.map((r) => ({
      value: r?.Role?.ID?.toString() || "",
      label: r?.Role?.Name || "",
    }));

    setRoleOptions(options);

    // map userRoles to options
    if (userRoles?.length) {
      const mapped = options.filter((opt) =>
        userRoles.some((ur) => ur.RoleID.toString() === opt.value)
      );
      setSelectedRoles(mapped);
    } else {
      setSelectedRoles([]);
    }
  }, [rolesData ]);

  const handleSave = () => {
    if (selectedRoles.length === 0) {
      onSave([]);
      onClose(false);
      return;
    }

    const roleIds = selectedRoles.map((role) => Number(role.value));
    onSave(roleIds);
    onClose(false);
  };

  const handleClose = () => {
    setSelectedRoles([]); // Reset selected roles on close
    onClose(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle className='text-center'>Attach Roles to User</DialogTitle>
          <DialogDescription className='text-center'>
            Assign roles to <strong>{userName}</strong>.
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-10'>
          <div className='flex flex-col'>
            <label className='text-foreground mb-2 text-sm font-semibold'>Name</label>
            <input
              type='text'
              value={userName}
              readOnly
              disabled
              className='border-input bg-muted text-muted-foreground h-10 w-full rounded-md border px-3 text-sm'
            />
          </div>

          {isLoading ? (
            <p>Loading roles...</p>
          ) : (
            <>
              <label className='text-foreground mb-2 text-sm font-semibold'>Role</label>

              <MultiSelect
                options={roleOptions}
                value={selectedRoles}
                onChange={setSelectedRoles}
                placeholder='Select roles...'
              />
            </>
          )}

          <div className='flex gap-3 pt-10'>
            <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
              Cancel
            </Button>
            <Button type='button' onClick={handleSave} className='flex-1'>
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
