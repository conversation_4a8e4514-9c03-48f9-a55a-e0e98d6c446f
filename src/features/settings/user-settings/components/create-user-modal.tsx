'use client';

import HookFormItem from '@/components/hook-form/HookFormItem';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useCreateUserMutation } from '@/hooks/api/use-user';
import { createUserSchema, type CreateUserSchema } from '@/validations/userSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';

type CreateUserModalProps = {
  trigger?: React.ReactNode;
  onCreated?: (created: unknown) => void;
};

export default function CreateUserModal({ trigger, onCreated }: CreateUserModalProps) {
  const [open, setOpen] = useState(false);

  const methods = useForm<CreateUserSchema>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      user_name: '',
      email: '',
      password: '',
      confirm_password: '',
    },
  });

  const {
    handleSubmit,
    reset,
    formState: { isSubmitting },
  } = methods;

  const { mutateAsync: createUser } = useCreateUserMutation();

  const handleOpenChange = (nextOpen: boolean) => {
    setOpen(nextOpen);
    if (!nextOpen) {
      reset();
    }
  };

  const onSubmit = async (data: CreateUserSchema) => {
    try {
      const { user_name, email, password } = data;
      const created = await createUser({ name: user_name, email, password });
      onCreated?.(created);
      setOpen(false);
    } catch (_e) {
      // Error toast is handled in the hook
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger ?? (
          <Button>
            <Plus className='size-4' />
            Create User
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className='mx-4 w-full sm:max-w-3xl'>
        <DialogHeader>
          <DialogTitle className='text-xl font-semibold'>Create New User</DialogTitle>
          <DialogDescription className='text-muted-foreground text-sm leading-relaxed'>
            To add a new user, fill in the details below.
          </DialogDescription>
        </DialogHeader>
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onSubmit)} className='space-y-6 py-2'>
            <div className='grid grid-cols-1 gap-x-4 gap-y-6 md:grid-cols-2'>
              <HookFormItem name='user_name' label='User Name' isRequired>
                <input
                  placeholder='e.g. John Doe'
                  className='border-input bg-background placeholder:text-muted-foreground focus:ring-ring w-full rounded-md border px-3 py-2.5 text-sm transition-colors focus:border-transparent focus:ring-2 focus:outline-none'
                />
              </HookFormItem>
              <HookFormItem name='email' label='Email' isRequired>
                <input
                  type='email'
                  placeholder='e.g. <EMAIL>'
                  className='border-input bg-background placeholder:text-muted-foreground focus:ring-ring w-full rounded-md border px-3 py-2.5 text-sm transition-colors focus:border-transparent focus:ring-2 focus:outline-none'
                />
              </HookFormItem>
              <HookFormItem name='password' label='Password' isRequired>
                <input
                  type='password'
                  placeholder='Enter a strong password'
                  className='border-input bg-background placeholder:text-muted-foreground focus:ring-ring w-full rounded-md border px-3 py-2.5 text-sm transition-colors focus:border-transparent focus:ring-2 focus:outline-none'
                />
              </HookFormItem>
              <HookFormItem name='confirm_password' label='Re-enter Password' isRequired>
                <input
                  type='password'
                  placeholder='Re-enter your password'
                  className='border-input bg-background placeholder:text-muted-foreground focus:ring-ring w-full rounded-md border px-3 py-2.5 text-sm transition-colors focus:border-transparent focus:ring-2 focus:outline-none'
                />
              </HookFormItem>
            </div>
            <DialogFooter className='gap-3 pt-4 sm:gap-2'>
              <Button
                variant='outline'
                onClick={() => setOpen(false)}
                disabled={isSubmitting}
                className='min-w-[80px]'
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isSubmitting} className='min-w-[100px]'>
                {isSubmitting ? 'Creating…' : 'Create User'}
              </Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
}
