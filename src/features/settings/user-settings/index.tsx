import ContentHeader from '../components/content-header';
import CreateUserModal from './components/create-user-modal';
import { createUserColumns } from './utils/columns';
import {
  DataTable,
  DataTableContainer,
  DataTableFooter,
  DataTableHeader,
} from '@/components/data-table';
import TablePagination from '@/components/shared/table-pagination';
import { Button } from '@/components/ui/button';
import { useUsersQuery } from '@/hooks/api/use-user';
import { useTableState } from '@/hooks/use-table-state';
import { Users, Plus } from 'lucide-react';
import { useMemo } from 'react';

export default function UserSettings() {
  const { pagination } = useTableState();

  const { data: userData, isLoading } = useUsersQuery({
    limit: pagination.pageSize,
    number: pagination.pageIndex + 1,
  });

  const columns = useMemo(() => createUserColumns(), []);

  return (
    <div className='h-full w-full bg-white p-8'>
      <ContentHeader
        title='Manage Users'
        description='Create, view and manage users in the system, including their roles.'
        icon={<Users className='size-6' />}
      />
      <div className='my-6'>
        <DataTableContainer>
          <DataTableHeader>
            <DataTableHeader.Left>
              <div className='flex flex-col items-start gap-2'>
                <h3 className='text-lg font-semibold'>Users</h3>
                <p className='text-muted-foreground text-sm'>
                  {userData?.data?.total || 0} {userData?.data.total === 1 ? 'user' : 'users'}{' '}
                  account
                </p>
              </div>
            </DataTableHeader.Left>
            <DataTableHeader.Right>
              <CreateUserModal
                trigger={
                  <Button size={'lg'}>
                    <Plus className='size-4' />
                    Create new user
                  </Button>
                }
              />
            </DataTableHeader.Right>
          </DataTableHeader>

          {!isLoading && userData?.data.total === 0 ? (
            <div className='flex flex-col items-center justify-center py-12 text-center'>
              <h3 className='mb-2 text-lg font-semibold'>No users found</h3>
              <p className='text-muted-foreground mb-4'>
                Add users to the system to manage their roles and permissions.
              </p>
            </div>
          ) : (
            <>
              <DataTable
                data={userData?.data.records || []}
                columns={columns}
                isLoading={isLoading}
              />
              <DataTableFooter>
                <TablePagination totalRecords={userData?.data.total || 0} />
              </DataTableFooter>
            </>
          )}
        </DataTableContainer>
      </div>
    </div>
  );
}
