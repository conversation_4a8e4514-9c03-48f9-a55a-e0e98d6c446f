import { UserListActions } from '../components/user-list-actions';
import LongText from '@/components/long-text';
import { Badge } from '@/components/ui';
import { getUserRoleBadgeColor, snakeToTitleCase } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';

export const createUserColumns = (): ColumnDef<User>[] => [
  {
    accessorKey: 'user_name',
    header: 'User Name',
    cell: ({ row }) => <div className='font-medium'>{row.getValue('user_name')}</div>,
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => (
      <LongText
        className='text-muted-foreground flex max-w-xs flex-wrap truncate'
        contentClassName='max-w-xs'
      >
        {row.getValue('email')}
      </LongText>
    ),
  },
  {
    accessorKey: 'roles',
    header: 'Roles',
    cell: ({ row }) => {
      const roles = row.getValue('roles') as { RoleName: string }[];
      if (!roles || roles.length === 0) {
        return <div className='text-muted-foreground'>N/A</div>;
      }

      return (
        <div className='flex max-w-md flex-wrap gap-2'>
          {roles.map((role, index) => (
            <Badge
              key={index}
              variant={getUserRoleBadgeColor(role ? role.RoleName.toLowerCase() : 'N/A')}
            >
              {snakeToTitleCase(role?.RoleName || 'N/A')}
            </Badge>
          ))}
        </div>
      );
    },
  },
  {
    accessorKey: 'actions',
    header: 'Actions',
    meta: {
      align: 'right',
    },
    cell: ({ row }) => {
      return <UserListActions user={row.original} />;
    },
  },
];
