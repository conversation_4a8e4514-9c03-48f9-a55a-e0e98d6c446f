import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useCreateJobRoleMutation } from '@/hooks/api/use-job';
import { Plus } from 'lucide-react';
import { useState, useCallback, ReactNode } from 'react';
import { toast } from 'sonner';

type CreateJobRoleDialogProps = {
  trigger?: ReactNode;
  onCreated?: (created: unknown) => void;
};

export default function CreateJobRoleDialog({ trigger, onCreated }: CreateJobRoleDialogProps) {
  const [open, setOpen] = useState(false);
  const [form, setForm] = useState({ name: '', description: '' });

  const { mutateAsync: createRole, isPending } = useCreateJobRoleMutation();

  const resetForm = useCallback(() => {
    setForm({ name: '', description: '' });
  }, []);

  const handleOpenChange = useCallback(
    (nextOpen: boolean) => {
      setOpen(nextOpen);
      if (!nextOpen) {
        resetForm();
      }
    },
    [resetForm]
  );

  const handleCreate = useCallback(async () => {
    const name = form.name.trim();
    const description = form.description.trim();

    if (!name) {
      toast.error('Job role name is required');
      return;
    }

    try {
      const created = await createRole({ name, description });
      onCreated?.(created);
      setOpen(false);
    } catch (_e) {
      // error toast is handled in the hook
    }
  }, [createRole, form.name, form.description, onCreated]);

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger ?? (
          <Button>
            <Plus className='size-4' />
            Add Job Role
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className='sm:max-w-lg'>
        <DialogHeader>
          <DialogTitle>Create New Job Role</DialogTitle>
          <DialogDescription>To add a new job role, fill in the details below.</DialogDescription>
        </DialogHeader>
        <div className='grid gap-6 py-4'>
          <div className='space-y-2'>
            <Label htmlFor='create-name'>Job Title *</Label>
            <Input
              id='create-name'
              placeholder='e.g. Senior Software Engineer'
              value={form.name}
              required
              onChange={(e) => setForm((prev) => ({ ...prev, name: e.target.value }))}
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='create-description'>Description</Label>
            <Textarea
              id='create-description'
              placeholder='Short description of the role responsibilities...'
              value={form.description}
              onChange={(e) => setForm((prev) => ({ ...prev, description: e.target.value }))}
              rows={3}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant='outline' onClick={() => setOpen(false)} disabled={isPending}>
            Cancel
          </Button>
          <Button onClick={handleCreate} disabled={isPending}>
            {isPending ? 'Creating…' : 'Create Role'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
