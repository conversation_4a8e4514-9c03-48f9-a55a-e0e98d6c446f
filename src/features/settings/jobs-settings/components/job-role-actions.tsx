import { ConfirmationModal } from '@/components/shared/confirmation-modal';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Pencil, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface JobRoleActionsProps {
  role: JobRole;
  onEdit: (role: JobRole) => void;
  onDelete: (roleId: string) => Promise<void>;
}

export function JobRoleActions({ role, onEdit, onDelete }: JobRoleActionsProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [formData, setFormData] = useState({ name: role.name, description: role.description });

  const handleEditClick = () => {
    setFormData({ name: role.name, description: role.description });
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    const updatedRole: JobRole = {
      ...role,
      name: formData.name.trim(),
      description: formData?.description?.trim(),
    };
    onEdit(updatedRole);
    setIsEditDialogOpen(false);
  };

  const handleDeleteConfirm = async () => {
    await onDelete(role.id);
  };

  return (
    <div className='flex items-center justify-end gap-2'>
      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogTrigger asChild>
          <Button variant='ghost' size='sm' onClick={handleEditClick}>
            <Pencil className='h-4 w-4' />
            <span className='sr-only'>Edit {role.name}</span>
          </Button>
        </DialogTrigger>
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>Edit Job Role</DialogTitle>
            <DialogDescription>Update the job role details below.</DialogDescription>
          </DialogHeader>
          <div className='grid gap-4 py-4'>
            <div className='space-y-2'>
              <Label htmlFor='edit-name'>Job Title *</Label>
              <Input
                id='edit-name'
                placeholder='e.g. Senior Software Engineer'
                value={formData.name}
                onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className='space-y-2'>
              <Label htmlFor='edit-description'>Description</Label>
              <Textarea
                id='edit-description'
                placeholder='Brief description of the role responsibilities...'
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant='outline' onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <ConfirmationModal
        title='Delete Job Role'
        description={`Are you sure you want to delete "${role.name}"? This action cannot be undone.`}
        onConfirm={handleDeleteConfirm}
      >
        <Button variant='ghost' size='sm'>
          <Trash2 className='text-destructive h-4 w-4' />
          <span className='sr-only'>Delete {role.name}</span>
        </Button>
      </ConfirmationModal>
    </div>
  );
}
