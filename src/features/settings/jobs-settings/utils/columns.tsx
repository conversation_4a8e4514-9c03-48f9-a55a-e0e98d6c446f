import { JobRoleActions } from '../components/job-role-actions';
import LongText from '@/components/long-text';
import { ColumnDef } from '@tanstack/react-table';

interface ColumnsProps {
  onEdit: (role: JobRole) => void;
  onDelete: (roleId: string) => Promise<void>;
}

export const createJobRoleColumns = ({ onEdit, onDelete }: ColumnsProps): ColumnDef<JobRole>[] => [
  {
    accessorKey: 'name',
    header: 'Job Role',
    cell: ({ row }) => <div className='font-medium'>{row.getValue('name')}</div>,
  },
  {
    accessorKey: 'description',
    header: 'Description',
    meta: {
      maxSize: '450',
    },
    cell: ({ row }) => {
      const description = row.getValue('description') as string;
      return (
        <LongText className='text-muted-foreground max-w-md'>
          {description || 'No description provided'}
        </LongText>
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    meta: {
      align: 'right',
    },
    cell: ({ row }) => {
      const role = row.original;
      return <JobRoleActions role={role} onEdit={onEdit} onDelete={onDelete} />;
    },
  },
];
