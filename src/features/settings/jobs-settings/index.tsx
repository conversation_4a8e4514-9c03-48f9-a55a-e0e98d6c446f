import ContentHeader from '../components/content-header';
import CreateJobRoleDialog from './components/create-job-role-dialog';
import { createJobRoleColumns } from './utils/columns';
import { JobIcon } from '@/assets/icons';
import {
  DataTable,
  DataTableContainer,
  DataTableHeader,
  DataTableFooter,
} from '@/components/data-table';
import TablePagination from '@/components/shared/table-pagination';
import { Button } from '@/components/ui/button';
import {
  useJobRolesQuery,
  useUpdateJobRoleMutation,
  useDeleteJobRoleMutation,
} from '@/hooks/api/use-job';
import { useTableState } from '@/hooks/use-table-state';
import { Plus } from 'lucide-react';
import { useMemo, useCallback } from 'react';
import { toast } from 'sonner';

export default function JobsSettings() {
  const { pagination } = useTableState();
  const { data: jobRoleData, isLoading } = useJobRolesQuery({
    limit: pagination.pageSize,
    offset: pagination.offset,
  });
  const { mutateAsync: updateRole } = useUpdateJobRoleMutation();
  const { mutateAsync: deleteRole } = useDeleteJobRoleMutation();

  const handleEditRole = useCallback(
    (updatedRole: JobRole) => {
      if (!updatedRole.name.trim()) {
        toast.error('Job role name is required');
        return;
      }
      updateRole({
        roleId: updatedRole.id,
        payload: {
          name: updatedRole.name.trim(),
          description: updatedRole.description?.trim(),
        },
      }).catch(() => {
        // error handled in hook
      });
    },
    [updateRole]
  );

  const handleDeleteRole = useCallback(
    async (roleId: string) => {
      try {
        await deleteRole(roleId);
      } catch (_e) {
        // error handled in hook
      }
    },
    [deleteRole]
  );

  const columns = useMemo(
    () =>
      createJobRoleColumns({
        onEdit: handleEditRole,
        onDelete: handleDeleteRole,
      }),
    [handleEditRole, handleDeleteRole]
  );

  return (
    <div className='h-full w-full bg-white p-8'>
      <ContentHeader
        title='Manage Job Roles'
        description='Create, edit, and delete job roles which can be assigned to job postings when creating a new job.'
        icon={<JobIcon />}
      />
      <div className='my-6'>
        <DataTableContainer>
          <DataTableHeader>
            <DataTableHeader.Left>
              <div className='flex flex-col items-start gap-2'>
                <h3 className='text-lg font-semibold'>Job Roles</h3>
                <p className='text-muted-foreground text-sm'>
                  {jobRoleData?.total || 0} {jobRoleData?.total === 1 ? 'role' : 'roles'} configured
                </p>
              </div>
            </DataTableHeader.Left>
            <DataTableHeader.Right>
              <CreateJobRoleDialog
                trigger={
                  <Button size={'lg'}>
                    <Plus className='size-4' />
                    Add Job Role
                  </Button>
                }
              />
            </DataTableHeader.Right>
          </DataTableHeader>

          {!isLoading && jobRoleData?.total === 0 ? (
            <div className='flex flex-col items-center justify-center py-12 text-center'>
              <h3 className='mb-2 text-lg font-semibold'>No job roles yet</h3>
              <p className='text-muted-foreground mb-4'>
                Get started by creating your first job role with "Add Role" button.
              </p>
            </div>
          ) : (
            <>
              <DataTable data={jobRoleData?.items || []} columns={columns} isLoading={isLoading} />
              <DataTableFooter>
                <TablePagination totalRecords={jobRoleData?.total || 0} />
              </DataTableFooter>
            </>
          )}
        </DataTableContainer>
      </div>
    </div>
  );
}
