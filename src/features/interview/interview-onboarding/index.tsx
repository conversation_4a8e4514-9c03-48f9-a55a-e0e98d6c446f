import { AIIcon } from '@/assets';
import { Button } from '@/components/ui';
import { useCandidateStore } from '@/stores/candidate-store';
import SessionStorageManager, { SESSION_STORAGE_KEYS } from '@/utils/sessionStorage';
import { useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';

const InterviewOnboarding = () => {
  const navigate = useNavigate();
  const { candidateId } = useCandidateStore();

  useEffect(() => {
    if (candidateId) {
      SessionStorageManager.setItem(SESSION_STORAGE_KEYS.CANDIDATE_DATA, {
        candidate_id: candidateId,
      });
    }
  }, [candidateId]);

  return (
    <>
      <div className='container flex p-3 md:p-6'>
        <div className='flex h-[calc(100vh-72px-12px-12px)] w-full flex-col rounded-2xl border md:h-[calc(100vh-72px-36px-36px)] md:flex-row'>
          <div
            className='hidden flex-7/12 rounded-l-2xl md:flex md:w-2/3'
            style={{
              backgroundImage: 'url(/images/previa-interview-onboarding.png)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              boxShadow: 'rgb(239 246 255) 0 -161px 100px 0 inset',
            }}
          >
            <div className='h-ful flex flex-col justify-end gap-6 p-14'>
              <AIIcon />
              <div className='flex flex-col gap-4'>
                <h1 className='text-3xl font-semibold text-black'>
                  Welcome,
                  <br />
                  You're Invited to an Interview
                </h1>
                <p className='text-gray-dark'>
                  This interview is part of your application for Internship at Vivasoft Ltd.
                </p>
              </div>
            </div>
          </div>
          <div className='w-full flex-5/12 md:w-1/3'>
            <div className='flex min-h-[calc(100vh-72px-12px-12px)] items-center justify-center md:min-h-[calc(100vh-72px-24px-24px)]'>
              <div className='w-full space-y-4 px-4 md:space-y-6 md:px-10'>
                <div className='mb-6 md:hidden'>
                  <div className='flex flex-col gap-4'>
                    <h1 className='text-3xl font-semibold text-black'>
                      Welcome,
                      <br />
                      You're Invited to a Demo Interview
                    </h1>
                  </div>
                </div>
                <h1 className='text-2xl font-semibold text-black'>Your Interview Journey</h1>

                <p className='text-gray-700'>
                  Welcome to your AI-powered interview experience with.
                  <strong> Previa</strong>. This is your opportunity to demonstrate your skills and
                  potential in a live video session.
                </p>

                <div className='border-primary-200 rounded-md border bg-gradient-to-bl from-blue-50 to-indigo-100 px-5 py-4 text-sm leading-[22px]'>
                  <p>
                    <strong>Before You Begin,</strong> To start this interview, you'll need to
                    enable your <strong>webcam and microphone with a laptop device</strong>. You'll
                    be speaking your answers aloud. we'll transcribe your responses for review.
                  </p>
                </div>

                <Button
                  onClick={() => {
                    navigate({ to: '/interview-onboarding-system-setup' });
                  }}
                  className='from-primary-500 to-primary-600 w-full bg-gradient-to-bl'
                >
                  Start Interview
                </Button>

                <p className='text-center text-xs text-gray-600 md:text-sm'>
                  Need any help? Please{' '}
                  <a
                    href='mailto:<EMAIL>'
                    className='text-primary-600 hover:text-primary-700 underline transition-colors'
                    title='<EMAIL>'
                  >
                    Contact Us
                  </a>{' '}
                  for assistance
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default InterviewOnboarding;
