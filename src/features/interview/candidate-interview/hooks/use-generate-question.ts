import { timerService } from '@/features/interview/candidate-interview/services/timerService';
import { currentQuestion } from '@/features/interview/candidate-interview/signals/interviewSignals';
import {
  canRequestNewQuestion,
  currentPhase,
  currentQuestionStatus,
  InterviewPhase,
  QuestionStatus,
} from '@/features/interview/candidate-interview/signals/timerSignals';
import { useGenerateQuestionSyncMutation } from '@/hooks/api/use-interview';
import { InterviewItem } from '@/services/interview';
import { InterviewSessionStorage } from '@/utils/sessionStorage';
import { useState, useRef } from 'react';

export function useGenerateQuestion() {
  const generateQuestionSyncMutation = useGenerateQuestionSyncMutation();
  const [showLongLoadingMessage, setShowLongLoadingMessage] = useState(false);
  const longLoadingTimerRef = useRef<number | null>(null);

  const generateQuestion = async (isLastQuestion: boolean = false) => {
    try {
      const interviewData = InterviewSessionStorage.getInterviewItems() as InterviewItem[];

      longLoadingTimerRef.current = window.setTimeout(() => {
        setShowLongLoadingMessage(true);
      }, 10000); // 1 minute

      const syncResponse = await generateQuestionSyncMutation.mutateAsync({
        channel_id: interviewData?.[0]?.id,
        is_interview_ending_question: isLastQuestion,
      });

      if (longLoadingTimerRef.current) {
        window.clearTimeout(longLoadingTimerRef.current);
        longLoadingTimerRef.current = null;
      }
      setShowLongLoadingMessage(false);

      return syncResponse;
    } catch (error) {
      console.error('Failed to generate question:', error);
      if (longLoadingTimerRef.current) {
        window.clearTimeout(longLoadingTimerRef.current);
        longLoadingTimerRef.current = null;
      }
      setShowLongLoadingMessage(false);
      throw error;
    }
  };

  const generateNextQuestion = async () => {
    // Clear current question and set to loading phase
    currentQuestion.value = {
      text: '',
      expectedDuration: 30,
      isLoading: true,
      questionId: '',
      audioData: undefined,
    };

    timerService.resetForNewQuestion();

    currentPhase.value = InterviewPhase.LOADING_QUESTION;

    const isLastQuestion = !canRequestNewQuestion.value;

    if (isLastQuestion) {
      currentQuestionStatus.value = QuestionStatus.IS_LAST_QUESTION;
    }

    try {
      const syncResponse = await generateQuestion(isLastQuestion);

      // Update current question with the new content
      const questionId = `q_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      currentQuestion.value = {
        text: syncResponse.content,
        expectedDuration: syncResponse.audio_length_in_milliseconds / 1000 || 30,
        isLoading: false,
        questionId: questionId,
        audioData: syncResponse.audio_data,
      };

      // Store updated session data
      InterviewSessionStorage.setChannelId(syncResponse.channel_id);
      InterviewSessionStorage.setChatId(syncResponse.chat_id);

      // Set phase to reading question
      currentPhase.value = InterviewPhase.READING_QUESTION;
      return syncResponse;
    } catch (error) {
      console.error('Failed to generate next question:', error);
      throw error;
    }
  };

  return {
    generateQuestion,
    generateNextQuestion,
    isGeneratingQuestion: generateQuestionSyncMutation.isPending,
    showLongLoadingMessage,
  };
}
