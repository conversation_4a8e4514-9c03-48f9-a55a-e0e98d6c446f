import { mediaService } from '../services/mediaService';
import { mediaState } from '../signals/interviewSignals';
import { useCandidateStore } from '@/stores/candidate-store';
import { useSignals } from '@preact/signals-react/runtime';
import { AlertCircle } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

export function VideoFeed() {
  useSignals();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(true);

  const { candidateDetails } = useCandidateStore();

  useEffect(() => {
    // Check for existing permissions on mount
    const checkPermissions = async () => {
      setIsCheckingPermissions(true);

      // If we don't have permission, check if it was granted before
      if (!mediaState.value.hasPermission) {
        const hasExistingPermission = await mediaService.checkExistingPermissions();
        if (!hasExistingPermission) {
          // Try to request permissions silently
          await mediaService.requestPermissions();
        }
      }

      setIsCheckingPermissions(false);
    };

    checkPermissions();
  }, []);

  useEffect(() => {
    if (mediaState.value.stream && videoRef.current) {
      videoRef.current.srcObject = mediaState.value.stream;

      // Listen for track ended events
      const videoTracks = mediaState.value.stream.getVideoTracks();
      const audioTracks = mediaState.value.stream.getAudioTracks();

      const handleTrackEnded = () => {
        console.log('Media track ended - permission may have been revoked');
      };

      [...videoTracks, ...audioTracks].forEach((track) => {
        track.addEventListener('ended', handleTrackEnded);
      });

      return () => {
        [...videoTracks, ...audioTracks].forEach((track) => {
          track.removeEventListener('ended', handleTrackEnded);
        });
      };
    }
  }, [mediaState.value.stream]);

  const handleRequestPermissions = async () => {
    setIsCheckingPermissions(true);
    await mediaService.requestPermissions();
    setIsCheckingPermissions(false);
  };

  return (
    <div className='relative overflow-hidden rounded-lg shadow-sm'>
      <video
        ref={videoRef}
        autoPlay
        muted
        className='max-h-[400px] min-h-[300px] w-full -scale-x-100 rounded-lg object-cover'
        style={{ backgroundColor: mediaState.value.hasPermission ? 'transparent' : '#000' }}
      />

      {/* Loading state */}
      {isCheckingPermissions && (
        <div className='bg-opacity-50 absolute inset-0 flex items-center justify-center rounded-lg bg-black text-white'>
          <div className='text-center'>
            <div className='mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-white'></div>
            <p>Checking camera permissions...</p>
          </div>
        </div>
      )}

      {/* No permission state */}
      {!mediaState.value.hasPermission && !isCheckingPermissions && (
        <div className='bg-opacity-50 absolute inset-0 flex items-center justify-center rounded-lg bg-black text-white'>
          <div className='text-center'>
            <AlertCircle className='mx-auto mb-2 h-8 w-8' />
            <p className='mb-2'>Camera not available</p>
            <button
              onClick={handleRequestPermissions}
              className='rounded bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700'
            >
              Enable Camera
            </button>
            {mediaState.value.error && (
              <p className='mt-2 text-xs text-red-300'>{mediaState.value.error}</p>
            )}
          </div>
        </div>
      )}

      {/* Meta Data */}
      <div className='absolute right-4 bottom-4 inline-flex items-center gap-2 rounded-full border border-white/10 bg-black/40 px-4 py-2 backdrop-blur-md'>
        <span className='font-medium text-white'>{candidateDetails?.name ?? 'Participant'}</span>
      </div>

      <div className='absolute top-4 left-4 inline-flex items-center gap-2 rounded-full border border-white/10 bg-black/40 px-4 py-2 backdrop-blur-md'>
        <div className='size-2 animate-pulse rounded-full bg-red-500' />
        <span className='font-medium text-white'>Recording</span>
      </div>
    </div>
  );
}
