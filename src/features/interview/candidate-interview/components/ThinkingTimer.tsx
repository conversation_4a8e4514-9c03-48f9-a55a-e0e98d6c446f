'use client';

import { timerService } from '../services/timerService';
import {
  currentPhase,
  defaultTimerConfig,
  InterviewPhase,
  thinkingTimeRemaining,
} from '../signals/timerSignals';
import { CircularTimer } from '@/components/shared/circular-timer';
import { Button } from '@/components/ui';
import { useSignals } from '@preact/signals-react/runtime';
import { ClockFading, Mic } from 'lucide-react';
import { useEffect } from 'react';

export function ThinkingTimer() {
  useSignals();

  // Register auto-submit callback for thinking phase
  useEffect(() => {
    const handleAutoSubmit = () => {
      handleReady();
    };

    timerService.registerAutoSubmitCallback('thinking', handleAutoSubmit);

    return () => {
      timerService.unregisterAutoSubmitCallback('thinking');
    };
  }, []);

  if (currentPhase.value !== InterviewPhase.THINKING) return null;

  const handleReady = () => {
    timerService.forceTransitionToAnswering();
  };

  return (
    <div className='flex h-full flex-col items-center justify-center rounded-lg bg-gray-50 p-8'>
      <div className='max-w-md text-center'>
        <h3 className='mb-4 text-2xl font-semibold text-gray-900'>Get Ready to Answer...</h3>

        <p className='mb-8 text-xs leading-relaxed text-gray-400'>
          After the 30-second thinking time, recording will start automatically, or you can begin
          recording early by clicking the button below.
        </p>

        <div className='mb-8 flex items-center justify-center gap-2'>
          <ClockFading className='size-4 text-orange-600' />
          <span className='text-gray-700'>Time to think: </span>
          <CircularTimer
            totalTime={defaultTimerConfig.THINKING_TIMER}
            currentTime={thinkingTimeRemaining.value}
          />
        </div>
      </div>
      <Button
        onClick={handleReady}
        className='flex w-full items-center justify-center gap-2 rounded-lg bg-[linear-gradient(141.88deg,#5C92FA_35.25%,#A75FFD_107.8%)]'
      >
        <Mic className='size-5 text-white' />
        Start Answering
      </Button>
    </div>
  );
}
