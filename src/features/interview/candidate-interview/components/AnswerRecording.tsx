import { mediaService } from '../services/mediaService';
import { sttService } from '../services/sttService';
import { timerService } from '../services/timerService';
import { answerState } from '../signals/interviewSignals';
import {
  answerTimeRemaining,
  currentPhase,
  defaultTimerConfig,
  InterviewPhase,
} from '../signals/timerSignals';
import SoundWaveAnimation from '@/assets/animations/SoundWaveAnimation';
import { CircularTimer } from '@/components/shared/circular-timer';
import { Button } from '@/components/ui';
import { InterviewSessionStorage } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { Check, ClockFading } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

export function AnswerRecording() {
  useSignals();

  const [_transcribedText, setTranscribedText] = useState('');
  const [_isTranscribing, setIsTranscribing] = useState(false);
  const chunksRef = useRef<Blob[]>([]);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const isRecordingRef = useRef(false);

  // Separate function to handle transcription and API call
  const handleTranscription = async (audioBlob: Blob) => {
    console.log('blob');
    const channelId = InterviewSessionStorage.getChannelId();
    const chatId = InterviewSessionStorage.getChatId();

    setIsTranscribing(true);
    answerState.value = {
      ...answerState.value,
      isTranscribing: true,
    };
    try {
      const transcription = await sttService.transcribeAudio(audioBlob, channelId, chatId);
      setTranscribedText(transcription);

      answerState.value = {
        ...answerState.value,
        recordingBlob: audioBlob,
        transcribedText: transcription,
        editedAnswer: transcription,
        isRecording: false,
      };
      currentPhase.value = InterviewPhase.EDITING;
      timerService.startEditTimer();
    } catch (_error) {
      setTranscribedText('Transcription failed.');

      answerState.value = {
        ...answerState.value,
        recordingBlob: audioBlob,
        transcribedText: '',
        isRecording: false,
      };
    } finally {
      setIsTranscribing(false);
    }
  };

  // Auto-start recording when entering ANSWERING phase
  useEffect(() => {
    if (currentPhase.value === InterviewPhase.ANSWERING && !isRecordingRef.current) {
      startRecording();
    }
  }, [currentPhase.value]);

  const startRecording = async () => {
    if (chunksRef.current) stopRecording();
    try {
      const recorder = await mediaService.startRecording();
      if (!recorder) {
        return;
      }

      chunksRef.current = [];
      setTranscribedText('');

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      recorder.onstop = async () => {
        if (currentPhase.value !== InterviewPhase.COMPLETED) {
          const blob = new Blob(chunksRef.current, { type: 'audio/webm' });
          await handleTranscription(blob);
        }
      };

      recorder.start();

      mediaRecorderRef.current = recorder;
      isRecordingRef.current = true;

      answerState.value = {
        ...answerState.value,
        isRecording: true,
      };

      // Register auto-submit callback with timer service
      timerService.startAnswerTimer(async () => {
        if (isRecordingRef.current && mediaRecorderRef.current) {
          mediaRecorderRef.current.stop();
          isRecordingRef.current = false;
          timerService.stopTimer('answer');
        } else {
          // If recorder is not available, create blob directly and call transcription
          const blob = new Blob(chunksRef.current, { type: 'audio/webm' });
          await handleTranscription(blob);
        }
      });
    } catch (_error) {
      setTranscribedText('Transcription failed.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecordingRef.current) {
      mediaRecorderRef.current.stop();
      isRecordingRef.current = false;
      timerService.stopTimer('answer');
    }
  };

  const handleManualStop = () => {
    stopRecording();
  };

  if (currentPhase.value !== InterviewPhase.ANSWERING) return null;

  return (
    <div className='flex h-full flex-col space-y-4'>
      {/* Header with timer */}
      <div className='mb-1 flex items-center justify-between'>
        <h3 className='text-2xl font-semibold text-black'>You may speak now. We're listening…</h3>
        <div className='flex items-center gap-2 text-sm'>
          <ClockFading className='size-4 text-orange-600' />
          <span className='text-gray-400'>Time to Talk:</span>
          <CircularTimer
            totalTime={defaultTimerConfig.ANSWERING_TIMER}
            currentTime={answerTimeRemaining.value}
          />
        </div>
      </div>

      <div>
        <span className='text-gray-light'>
          Done talking? Click the button to review your answer
        </span>
      </div>

      {/* Recording status */}
      {/* <div className='rounded-lg p-4'> */}
      <div className='flex h-full w-full items-center justify-center'>
        <SoundWaveAnimation />
      </div>

      <Button
        onClick={handleManualStop}
        disabled={!isRecordingRef.current}
        className='flex w-full items-center justify-center gap-2 rounded-md bg-[linear-gradient(141.88deg,#5C92FA_35.25%,#A75FFD_107.8%)] disabled:opacity-50'
      >
        <Check className='size-4' />
        Transcribe my answer
      </Button>
      {/* </div> */}
    </div>
  );
}
