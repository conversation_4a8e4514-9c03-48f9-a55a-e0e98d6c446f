'use client';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useAddFeedbackMutation, useRunEvaluationQuery } from '@/hooks/api/use-interview';
import SessionStorageManager, {
  InterviewSessionStorage,
  SESSION_STORAGE_KEYS,
} from '@/utils/sessionStorage';
import { motion } from 'framer-motion';
import { CheckCircle, HelpCircle, Mail, Star } from 'lucide-react';
import type React from 'react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function InterviewFeedbackForm() {
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const channelId = InterviewSessionStorage.getChannelId();

  useRunEvaluationQuery(channelId ?? '');

  const { mutate: addFeedback, isPending } = useAddFeedbackMutation();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!channelId) {
      toast.error('No interview found');
      return;
    }

    addFeedback(
      {
        interviewId: channelId,
        feedback,
      },
      {
        onSuccess: () => {
          setIsSubmitted(true);
          SessionStorageManager.removeItem(SESSION_STORAGE_KEYS.CHANNEL_ID);
        },
      }
    );
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  if (isSubmitted || !channelId) {
    return (
      <div className='flex h-[calc(100vh-68px)] items-center justify-center bg-gray-100 p-4'>
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className='max-w-lg text-center'
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className='mb-6 rounded-3xl bg-white p-8'
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.8, type: 'spring', stiffness: 200 }}
              className='mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-50'
            >
              <CheckCircle className='h-8 w-8 text-green-600' />
            </motion.div>

            <motion.h2
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.9, duration: 0.5 }}
              className='mb-3 text-2xl font-semibold text-gray-900'
            >
              Thanks for Your Feedback!
            </motion.h2>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 0.5 }}
              className='mb-6 text-lg leading-relaxed text-gray-600'
            >
              Your response has been recorded and will help us improve our AI interview platform for
              future candidates.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1, duration: 0.5 }}
              className='mb-6 rounded-2xl bg-blue-50 p-6'
            >
              <div className='mb-3 flex items-center justify-center gap-2'>
                <HelpCircle className='h-5 w-5 text-blue-600' />
                <h3 className='text-gray-dark text-lg font-medium'>Need Help?</h3>
              </div>
              <p className='text-gray-light mb-4 text-sm'>
                Have questions about your interview or our platform? We're here to help!
              </p>
              <Button
                className='from-primary-500 to-primary-600 rounded-xl bg-gradient-to-r !px-4 text-sm text-white hover:from-blue-600 hover:to-blue-700'
                onClick={() => (window.location.href = 'mailto:<EMAIL>')}
                size={'lg'}
              >
                <Mail className='mr-2 h-4 w-4' />
                Contact Support
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className='flex h-[calc(100vh-68px)] items-center justify-center bg-gray-100 p-4'>
      <motion.div
        variants={containerVariants}
        initial='hidden'
        animate='visible'
        className='w-full max-w-xl'
      >
        <motion.div variants={itemVariants}>
          <div className='mx-auto rounded-3xl bg-white p-8'>
            <div className='relative mb-8'>
              <div className='relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 p-6 text-center'>
                {/* Confetti dots */}
                <div className='absolute inset-0'>
                  <div className='absolute top-2 left-4 h-2 w-2 rounded-full bg-yellow-300'></div>
                  <div className='absolute top-4 right-6 h-1.5 w-1.5 rounded-full bg-orange-300'></div>
                  <div className='absolute bottom-3 left-8 h-1 w-1 rounded-full bg-pink-300'></div>
                  <div className='absolute right-4 bottom-2 h-2 w-2 rounded-full bg-yellow-400'></div>
                  <div className='absolute top-6 left-1/3 h-1.5 w-1.5 rounded-full bg-orange-400'></div>
                  <div className='absolute right-1/3 bottom-4 h-1 w-1 rounded-full bg-pink-400'></div>
                </div>
                {/* Streamers */}
                <div className='absolute inset-0'>
                  <div className='absolute top-1 left-6 h-0.5 w-8 rotate-12 transform rounded-full bg-yellow-300'></div>
                  <div className='absolute top-3 right-8 h-0.5 w-6 -rotate-12 transform rounded-full bg-orange-300'></div>
                  <div className='absolute bottom-2 left-1/4 h-0.5 w-10 rotate-45 transform rounded-full bg-pink-300'></div>
                  <div className='absolute right-1/4 bottom-1 h-0.5 w-7 -rotate-45 transform rounded-full bg-yellow-400'></div>
                </div>

                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.3, type: 'spring', stiffness: 200 }}
                  className='relative z-10 mb-2 text-4xl'
                >
                  🎉
                </motion.div>
              </div>
            </div>

            <div className='mb-8 text-center'>
              <motion.div variants={itemVariants} className='mb-6'>
                <h1 className='mb-2 text-3xl font-bold text-gray-900'>Thank You!</h1>
                <p className='text-lg text-gray-600'>
                  Your interview has been completed successfully.
                </p>
              </motion.div>

              <h2 className='text-primary mb-6 text-2xl font-medium'>Share your feedback</h2>

              <div className='mb-6 flex justify-center gap-1'>
                {[1, 2, 3, 4, 5].map((star) => (
                  <motion.button
                    key={star}
                    type='button'
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setRating(star)}
                    className='p-1'
                  >
                    <Star
                      className={`h-8 w-8 transition-colors ${
                        star <= rating
                          ? 'fill-orange-300 text-orange-300'
                          : 'text-gray-300 hover:text-orange-300'
                      }`}
                    />
                  </motion.button>
                ))}
              </div>
            </div>

            <form onSubmit={handleSubmit} className='space-y-6'>
              <motion.div variants={itemVariants} className='space-y-3'>
                <p className='text-sm font-medium text-gray-700'>
                  Any suggestions for further improvement?
                </p>
                <Textarea
                  placeholder='Your feedback'
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  className='gradient-border min-h-[100px] resize-none !rounded-xl !border-2 p-4 focus:outline-none focus-visible:ring-0'
                  required
                />
                <p className='text-xs text-gray-500'>
                  Please avoid sharing specific interview questions or personal details.
                </p>
              </motion.div>

              <motion.div variants={itemVariants}>
                <Button
                  type='submit'
                  className='from-primary-500 to-primary-600 hover:from-primary-600 w-full rounded-xl bg-gradient-to-r text-base font-medium text-white transition-all duration-200 hover:to-blue-700'
                  disabled={!feedback.trim()}
                  loading={isPending}
                >
                  Send Feedback
                </Button>
              </motion.div>
            </form>

            <motion.div variants={itemVariants} className='mt-8 border-t border-gray-100 pt-6'>
              <div className='text-center'>
                <div className='mb-2 flex items-center justify-center gap-2'>
                  <HelpCircle className='h-4 w-4 text-gray-500' />
                  <h3 className='text-sm font-medium text-gray-700'>Need Help?</h3>
                </div>
                <p className='mb-3 text-xs text-gray-500'>
                  Questions about your interview or technical issues?
                </p>
                <Button
                  variant='ghost'
                  size='sm'
                  className='text-primary-600 hover:bg-primary-50 hover:text-primary-700 rounded-xl text-sm'
                  onClick={() => (window.location.href = 'mailto:<EMAIL>')}
                >
                  <Mail className='mr-1 h-3 w-3' />
                  Contact Support
                </Button>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
