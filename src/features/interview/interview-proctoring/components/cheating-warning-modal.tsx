'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  Eye,
  Monitor,
  Shield,
  Clock,
  Focus,
  Keyboard,
  MousePointerClick,
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface CheatingWarningModalProps {
  isOpen: boolean;
  onClose: () => void;
  warningCount: number;
  violationType: ViolationType;
  onDisqualify: () => void;
}

const MAX_WARNINGS = 5;

export function CheatingWarningModal({
  isOpen,
  onClose,
  warningCount,
  violationType,
  onDisqualify,
}: CheatingWarningModalProps) {
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    if (!isOpen) return;

    if (warningCount >= MAX_WARNINGS) {
      onDisqualify();
      return;
    }

    // const timer = setInterval(() => {
    //   setCountdown((prev) => {
    //     if (prev <= 1) {
    //       onClose();
    //       return 10;
    //     }
    //     return prev - 1;
    //   });
    // }, 1000);

    // return () => clearInterval(timer);
  }, [isOpen, warningCount, onClose, onDisqualify]);

  useEffect(() => {
    if (isOpen) {
      setCountdown(10);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const getViolationMessage = () => {
    switch (violationType) {
      case 'tab_switch':
        return {
          title: 'Tab Switch Detected',
          description: 'You attempted to switch to another tab or reload application.',
          icon: <Eye className='h-5 w-5' />,
        };
      case 'fullscreen_exit':
        return {
          title: 'Full Screen Exit Detected',
          description: 'You attempted to exit full screen mode.',
          icon: <Monitor className='h-5 w-5' />,
        };
      case 'focus_loss':
        return {
          title: 'Focus Loss Detected',
          description: 'You lost focus from the interview window.',
          icon: <Focus className='h-5 w-5' />,
        };
      case 'blocked_shortcut':
        return {
          title: 'Keyboard Shortcut Blocked',
          description: 'You attempted to use a blocked keyboard shortcut.',
          icon: <Keyboard className='h-5 w-5' />,
        };
      case 'right_click_attempt':
        return {
          title: 'Right Click Attempted',
          description: 'You attempted to use the right click.',
          icon: <MousePointerClick className='h-5 w-5' />,
        };
      case 'alt_tab_attempt':
        return {
          title: 'Alt + Tab Attempted',
          description: 'You attempted to use the Alt + Tab shortcut.',
          icon: <Keyboard className='h-5 w-5' />,
        };
      case 'blocked_function_key':
        return {
          title: 'Function Key Blocked',
          description: 'You attempted to use a blocked function key.',
          icon: <Keyboard className='h-5 w-5' />,
        };

      default:
        return {
          title: 'Violation Detected',
          description: 'You attempted to perform an action that is not allowed.',
          icon: <AlertTriangle className='h-5 w-5' />,
        };
    }
  };

  const violation = getViolationMessage();
  const remainingWarnings = MAX_WARNINGS - warningCount;

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4 backdrop-blur-sm'>
      <div className='w-full max-w-md rounded-2xl border border-gray-100 bg-white shadow-xl'>
        <div className='p-6 pb-0'>
          <div className='mb-6 flex items-start gap-4'>
            <div className='flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-2xl bg-red-50'>
              <AlertTriangle className='h-6 w-6 text-red-500' />
            </div>
            <div className='flex-1'>
              <h2 className='mb-1 text-xl font-semibold text-gray-900'>Security Violation</h2>
              <div className='flex items-center gap-2'>
                <span className='text-sm text-gray-500'>Warning</span>
                <div className='flex gap-1'>
                  {Array.from({ length: MAX_WARNINGS }).map((_, i) => (
                    <div
                      key={i}
                      className={`h-2 w-2 rounded-full ${i <= warningCount - 1 ? 'bg-red-500' : 'bg-gray-200'}`}
                    />
                  ))}
                </div>
                <span className='text-sm text-gray-500'>
                  ({warningCount}/{MAX_WARNINGS})
                </span>
              </div>
            </div>
          </div>

          <div className='mb-5 rounded-2xl border border-red-100 bg-gradient-to-r from-red-50 to-orange-50 p-5'>
            <div className='flex items-start gap-4'>
              <div className='flex h-10 w-10 items-center justify-center rounded-xl bg-white shadow-sm'>
                {violation.icon}
              </div>
              <div className='flex-1'>
                <h3 className='mb-2 font-semibold text-gray-900'>{violation.title}</h3>
                <p className='text-sm leading-relaxed text-gray-700'>{violation.description}</p>
                <div className='mt-3 flex items-center gap-2 text-xs text-red-600'>
                  <Shield className='h-3 w-3' />
                  <span>This action is monitored for interview integrity</span>
                </div>
              </div>
            </div>
          </div>

          <div className='mb-5 grid grid-cols-2 gap-3'>
            <div className='rounded-xl border border-amber-200 bg-amber-50 p-4'>
              <div className='text-center'>
                <div className='mb-1 text-2xl font-bold text-amber-600'>{remainingWarnings}</div>
                <div className='text-xs font-medium text-amber-700'>Warnings Left</div>
              </div>
            </div>
            <div className='border-primary-100 bg-primary-50 rounded-xl border p-4'>
              <div className='text-center'>
                <div className='mb-1 flex items-center justify-center gap-1.5'>
                  <Clock className='text-primary-600 h-4 w-4' />
                  <span className='text-primary-600 text-2xl font-bold'>{countdown}</span>
                </div>
                <div className='text-primary-700 text-xs font-medium'>Auto Close</div>
              </div>
            </div>
          </div>

          {remainingWarnings === 1 && (
            <div className='mb-5 rounded-2xl bg-gradient-to-r from-red-600/90 to-red-500/90 p-4 text-white'>
              <div className='flex items-center gap-3'>
                <div className='flex h-8 w-8 items-center justify-center rounded-full bg-white/20'>
                  <AlertTriangle className='h-4 w-4' />
                </div>
                <div>
                  <div className='text-sm font-semibold'>Final Warning</div>
                  <div className='text-xs text-red-100'>
                    Next violation will disqualify you from this interview
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className='p-6 pt-0'>
          <Button
            onClick={onClose}
            className='h-12 w-full rounded-xl bg-gray-800 text-sm font-medium text-white hover:bg-gray-800'
          >
            I Understand - Continue Interview
          </Button>
          <p className='text-gray-light mt-3 text-center text-xs'>
            Please maintain focus and avoid switching tabs, exiting fullscreen, using shortcuts or
            right-clicking.
          </p>
        </div>
      </div>
    </div>
  );
}
