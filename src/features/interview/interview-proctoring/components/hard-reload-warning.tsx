'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { cn } from '@/libs/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, Play } from 'lucide-react';

interface Props {
  showFullScreenWarning: boolean;
  logViolation: (violation: Violation) => void;
}

const HardReloadWarning = ({ showFullScreenWarning, logViolation }: Props) => {
  return (
    <div>
      <AnimatePresence>
        {showFullScreenWarning && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='fixed inset-0 z-[99] grid place-items-center overflow-y-scroll bg-slate-900/20 backdrop-blur'
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              onClick={(e) => e.stopPropagation()}
              className={cn(
                'relative w-full max-w-lg cursor-default overflow-hidden rounded-lg bg-white text-gray-500 shadow-xl'
              )}
            >
              <Card className='w-full overflow-hidden bg-white pb-3 shadow-lg'>
                <div className='absolute inset-x-0 top-0 h-1 bg-gradient-to-r from-red-500 to-red-600' />
                <CardHeader className='space-y-1 pb-2'>
                  <CardTitle className='flex items-center gap-2 text-xl font-semibold text-red-500'>
                    <AlertCircle className='size-5 text-red-500' />
                    Hard Reload Detected
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className='text-sm leading-relaxed font-medium text-gray-500'>
                    Reloading this page during the session is strictly prohibited. Any attempt to do
                    so will be recorded and may lead to penalties. Please continue without
                    refreshing.
                  </p>
                </CardContent>
                <CardFooter className='px-6 pt-2 pb-4'>
                  <Button
                    className='w-full'
                    variant={'secondary'}
                    onClick={() => {
                      logViolation({
                        type: 'fullscreen_exit',
                        severity: 'high',
                        details: {
                          action: 'fullscreen_exit',
                          reason: 'Attempted to exit full screen mode',
                        },
                      });
                      // void handleFullScreenClose();
                    }}
                  >
                    <Play className='size-4' />
                    Continue Session
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
export default HardReloadWarning;
