import { AlertTrian<PERSON>, <PERSON> } from 'lucide-react';

interface Props {
  violations: ViolationLogger[];
}

export function DisqualificationScreen({ violations }: Props) {
  return (
    <div className='fixed inset-0 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4'>
      <div className='w-full max-w-lg overflow-hidden rounded-3xl bg-white shadow-2xl'>
        <div className='bg-gradient-to-tr from-red-600/90 to-red-500/90 p-8 text-center'>
          <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-white/20 backdrop-blur-sm'>
            <AlertTriangle className='h-8 w-8 text-white' />
          </div>
          <h1 className='mb-2 text-2xl font-bold text-white'>Interview Terminated</h1>
          <p className='text-sm text-red-100'>Session ended due to security policy violations</p>
        </div>

        <div className='space-y-6 p-8'>
          <div className='space-y-3 text-center'>
            <p className='text-gray-dark leading-relaxed'>
              Your interview session has been automatically terminated after reaching the maximum
              number of security warnings.
            </p>
          </div>

          <div className='rounded-2xl border border-gray-200 bg-gray-50 p-6'>
            <div className='mb-4 flex items-center gap-3'>
              <div className='-ml-1 flex items-center justify-center rounded-lg bg-red-100 p-2'>
                <Clock className='h-4 w-4 text-red-600' />
              </div>
              <h3 className='font-semibold text-gray-900'>Security Violations</h3>
            </div>
            <div className='space-y-2 text-sm text-gray-600'>
              {violations.map((violation) => (
                <div key={violation.id} className='flex items-center gap-2'>
                  <div className='h-1.5 w-1.5 rounded-full bg-red-400'></div>
                  <span>{violation.details.reason}</span>
                </div>
              ))}
            </div>
          </div>

          <div className='space-y-3'>
            <p className='text-gray-light text-center text-sm'>
              Please contact the interview administrator{' '}
              <a
                href='mailto:<EMAIL>'
                title='<EMAIL>'
                className='text-primary-500 font-medium'
              >
                Contact Support
              </a>{' '}
              if you believe this was an error.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
