import { CheatingWarningModal } from './components/cheating-warning-modal';
import { DisqualificationScreen } from './components/disqualification-screen';
import HardReloadWarning from './components/hard-reload-warning';
import {
  useKeyboardPrevention,
  useTabSwitchMonitor,
  useTextSelectionPrevention,
  useViolationLogger,
} from './hooks';
import useFullScreen from '@/hooks/use-full-screen';
import { useEffect, useState } from 'react';

interface Props {
  children: React.ReactNode;
}

export const InterviewProctoring = ({ children }: Props) => {
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [showDisqualificationScreen, setShowDisqualificationScreen] = useState(false);
  const [violationType, setViolationType] = useState<ViolationType>('fullscreen_exit');

  const { logViolation, violationCount, logList } = useViolationLogger({
    maxViolations: 5,
    onViolation: (violation) => {
      setShowWarningModal(true);
      setViolationType(violation.type as ViolationType);
    },
  });

  const { enterFullScreen, isFullScreen } = useFullScreen({
    onFullScreenChange(isFullScreen) {
      if (!isFullScreen) {
        setShowWarningModal(true);
        logViolation({
          type: 'fullscreen_exit',
          severity: 'high',
          details: {
            action: 'fullscreen_exit',
            reason: 'Attempted to exit full screen mode',
          },
        });
      }
    },
  });

  useEffect(() => {
    void enterFullScreen();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useTabSwitchMonitor(logViolation);
  useKeyboardPrevention(logViolation);
  useTextSelectionPrevention();

  const handleDisqualify = () => {
    setShowWarningModal(false);
    setShowDisqualificationScreen(true);
  };

  if (showDisqualificationScreen) {
    return <DisqualificationScreen violations={logList} />;
  }

  return (
    <>
      <div className='relative h-full w-full'>
        {/* Only in dev mode */}
        {import.meta.env.MODE === 'development' && (
          <div className='bg-primary-50 fixed top-4 left-4 z-50 border p-4 shadow-lg'>
            Violation Detected: {violationCount}
          </div>
        )}

        {children}
      </div>

      <CheatingWarningModal
        warningCount={violationCount}
        isOpen={showWarningModal}
        violationType={violationType}
        onClose={() => {
          setShowWarningModal(false);
          enterFullScreen();
        }}
        onDisqualify={handleDisqualify}
      />

      {!showWarningModal && (
        <HardReloadWarning showFullScreenWarning={!isFullScreen} logViolation={logViolation} />
      )}
    </>
  );
};
