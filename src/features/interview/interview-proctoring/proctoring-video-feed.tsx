import { useModelLoader } from './hooks/useModelLoader';
import { useCallback, useEffect, useRef, useState } from 'react';

const UserCamera = () => {
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const animationFrameRef = useRef(null);
  const fpsCounterRef = useRef({ lastTime: 0, frames: 0, fps: 0 });
  const referenceRef = useRef({ x: 0.5, y: 0.5, noseX: 0.5, noseY: 0.5 });

  // Move engagement data to refs to prevent re-renders during animation
  const engagementRef = useRef({ value: 100, isLookingAway: false });
  const lastStateUpdateRef = useRef(0);

  // Use refs for frequently accessed values to avoid re-renders
  const settingsRef = useRef({
    showLandmarks: true,
    showEngagementBar: true,
    gazeAnalysisEnabled: true,
  });

  const {
    faceLandmarkerRef,
    isModelLoaded,
    statusMessage,
    setStatusMessage,
    loadingProgress,
    permissionDenied,
    clearCache,
  } = useModelLoader();

  const [referenceSet, setReferenceSet] = useState(false);
  const [engagement, setEngagement] = useState(100);
  const [isLookingAway, setIsLookingAway] = useState(false);
  const [showLandmarks, setShowLandmarks] = useState(true);
  const [showEngagementBar, setShowEngagementBar] = useState(true);
  const [gazeAnalysisEnabled, setGazeAnalysisEnabled] = useState(true);
  const [fps, setFps] = useState(0);

  const [multipleFacesDetected, setMultipleFacesDetected] = useState(false);

  // Sync state with refs for performance
  useEffect(() => {
    settingsRef.current.showLandmarks = showLandmarks;
  }, [showLandmarks]);

  useEffect(() => {
    settingsRef.current.showEngagementBar = showEngagementBar;
  }, [showEngagementBar]);

  useEffect(() => {
    settingsRef.current.gazeAnalysisEnabled = gazeAnalysisEnabled;
  }, [gazeAnalysisEnabled]);

  // Memoized FPS update function
  const updateFPS = useCallback(() => {
    const now = performance.now();
    const counter = fpsCounterRef.current;
    counter.frames++;

    if (now - counter.lastTime >= 1000) {
      const newFps = Math.round((counter.frames * 1000) / (now - counter.lastTime));
      setFps(newFps);
      counter.frames = 0;
      counter.lastTime = now;
    }
  }, []);

  // Optimized gaze analysis function with throttling
  const analyzeGaze = useCallback((landmarks) => {
    if (!settingsRef.current.gazeAnalysisEnabled || landmarks.length < 468) {
      return;
    }

    const leftEye = landmarks[33];
    const rightEye = landmarks[263];
    const noseTip = landmarks[1];

    if (!leftEye || !rightEye || !noseTip) return;

    const eyeCenterX = (leftEye.x + rightEye.x) * 0.5;
    const eyeCenterY = (leftEye.y + rightEye.y) * 0.5;
    const reference = referenceRef.current;

    const dx = eyeCenterX - reference.x;
    const dy = eyeCenterY - reference.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    const verticalTilt = Math.abs(noseTip.y - reference.noseY) * 100;
    const horizontalTilt = Math.abs(noseTip.x - reference.noseX) * 100;

    const newEngagement = Math.max(0, 100 - (distance * 200 + verticalTilt + horizontalTilt));
    const lookingAway = newEngagement < 85;

    // Update refs immediately for smooth rendering
    engagementRef.current.value = newEngagement;
    engagementRef.current.isLookingAway = lookingAway;

    // Only update React state occasionally to prevent re-renders
    const now = performance.now();
    if (now - lastStateUpdateRef.current > 100) {
      // Update every 100ms
      setEngagement(newEngagement);
      setIsLookingAway(lookingAway);
      lastStateUpdateRef.current = now;
    }
  }, []);

  const analyzeFace = useCallback((results) => {
    if (!results.faceLandmarks) {
      // logViolation("faceNotFound", "high", { duration: performance.now() });
      return null;
    }

    // Check for multiple faces
    if (results.faceLandmarks.length > 1) {
      setMultipleFacesDetected(true);
      // logViolation("multipleFaces", "high", {
      //   facesDetected: results.faceLandmarks.length,
      // });
    } else {
      setMultipleFacesDetected(false);
    }

    const landmarks = results.faceLandmarks[0];
    if (!landmarks || landmarks.length < 468) return null;

    return landmarks;
  }, []);

  // Optimized drawing function with reduced canvas operations
  const drawLandmarks = useCallback(
    (landmarks) => {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      if (!canvas || !landmarks || !video) return;

      const ctx = canvas.getContext('2d');
      const { width, height } = canvas;
      const settings = settingsRef.current;

      // Always clear and draw video frame first
      ctx.clearRect(0, 0, width, height);
      ctx.drawImage(video, 0, 0, width, height);

      // Only draw overlays if needed
      if (settings.showLandmarks) {
        ctx.fillStyle = '#00FF00';
        // Use fillRect for better performance than arc
        for (let i = 0; i < landmarks.length; i++) {
          const lm = landmarks[i];
          ctx.fillRect(lm.x * width - 0.5, lm.y * height - 0.5, 1, 1);
        }
      }

      // Draw engagement bar separately to avoid coupling with landmarks
      if (settings.showEngagementBar && settings.gazeAnalysisEnabled && referenceSet) {
        drawEngagementBar(ctx);
      }
    },
    [referenceSet]
  );

  // Separate function for engagement bar to reduce coupling
  const drawEngagementBar = useCallback((ctx) => {
    // Use ref value for smooth animation without re-renders
    const attentionLevel = engagementRef.current.value / 100;
    const barHeight = 100;
    const barWidth = 20;
    const barX = 10;
    const barY = 10;

    // Background bar
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.fillRect(barX, barY, barWidth, barHeight);

    // Engagement level bar
    ctx.fillStyle = `rgba(${Math.floor(
      255 * (1 - attentionLevel)
    )}, ${Math.floor(255 * attentionLevel)}, 0, 0.8)`;
    const fillHeight = barHeight * attentionLevel;
    ctx.fillRect(barX, barY + barHeight - fillHeight, barWidth, fillHeight);

    // Current level indicator
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(barX, barY + barHeight * (1 - attentionLevel), barWidth, 2);
  }, []);

  // Main render loop with frame rate limiting
  const renderLoop = useCallback(() => {
    const video = videoRef.current;
    const faceLandmarker = faceLandmarkerRef.current;

    if (!video || video.readyState < 2 || !faceLandmarker) {
      animationFrameRef.current = requestAnimationFrame(renderLoop);
      return;
    }

    updateFPS();

    try {
      const startTimeMs = performance.now();
      const results = faceLandmarker.detectForVideo(video, startTimeMs);

      if (results.faceLandmarks?.length > 0) {
        const landmarks = analyzeFace(results);
        // const landmarks = results.faceLandmarks[0];
        drawLandmarks(landmarks);

        // Only analyze gaze if reference is set and gaze analysis is enabled
        if (referenceSet && settingsRef.current.gazeAnalysisEnabled) {
          analyzeGaze(landmarks);
        }
      } else {
        // No face detected - just draw video frame
        const canvas = canvasRef.current;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        }
      }
    } catch (error) {
      console.error('Face detection error:', error);
    }

    animationFrameRef.current = requestAnimationFrame(renderLoop);
  }, [updateFPS, drawLandmarks, analyzeGaze, referenceSet]);

  // Camera setup effect
  useEffect(() => {
    if (!isModelLoaded) return;

    const setupCamera = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 },
            facingMode: 'user',
          },
        });

        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          videoRef.current.onloadeddata = () => {
            renderLoop();
          };
        }
      } catch (err) {
        console.error('Camera access error:', err);
        setStatusMessage('Camera access failed');
      }
    };

    setupCamera();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      // Clean up video stream
      const currentVideoRef = videoRef.current;
      if (currentVideoRef?.srcObject) {
        const tracks = currentVideoRef.srcObject.getTracks();
        tracks.forEach((track) => track.stop());
      }
    };
  }, [isModelLoaded, renderLoop]);

  // Set reference position
  const setReference = useCallback(() => {
    const faceLandmarker = faceLandmarkerRef.current;
    const video = videoRef.current;

    if (!faceLandmarker || !video) return;

    try {
      const startTimeMs = performance.now();
      const results = faceLandmarker.detectForVideo(video, startTimeMs);

      if (results.faceLandmarks?.[0]) {
        const landmarks = results.faceLandmarks[0];
        const leftEye = landmarks[33];
        const rightEye = landmarks[263];
        const noseTip = landmarks[1];

        if (leftEye && rightEye && noseTip) {
          referenceRef.current = {
            x: (leftEye.x + rightEye.x) * 0.5,
            y: (leftEye.y + rightEye.y) * 0.5,
            noseX: noseTip.x,
            noseY: noseTip.y,
          };
          setReferenceSet(true);
          setStatusMessage('Reference set. Gaze tracking active.');
        }
      }
    } catch (error) {
      console.error('Set reference error:', error);
      setStatusMessage('Failed to set reference');
    }
  }, [setStatusMessage]);

  // Toggle handlers
  const handleToggleGazeAnalysis = useCallback((enabled) => {
    setGazeAnalysisEnabled(enabled);
    if (!enabled) {
      setEngagement(100);
      setIsLookingAway(false);
      setReferenceSet(false);
    }
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      {permissionDenied ? (
        <div
          style={{
            color: 'red',
            padding: '20px',
            border: '1px solid red',
            borderRadius: '5px',
          }}
        >
          <h2>Camera Permission Denied</h2>
          <p>Please allow camera access to use this application.</p>
        </div>
      ) : (
        <>
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '20px',
              marginBottom: '20px',
            }}
          >
            <div style={{ position: 'relative' }}>
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                style={{
                  width: '320px',
                  height: '240px',
                  borderRadius: '8px',
                  background: '#000',
                  transform: 'scaleX(-1)',
                }}
              />
              <canvas
                ref={canvasRef}
                width='320'
                height='240'
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '320px',
                  height: '240px',
                }}
              />
            </div>

            <div style={{ minWidth: '300px' }}>
              <div
                style={{
                  marginBottom: '20px',
                  padding: '15px',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '5px',
                }}
              >
                <h4 style={{ margin: '0 0 10px 0' }}>Controls</h4>

                <button
                  onClick={setReference}
                  disabled={!isModelLoaded || !gazeAnalysisEnabled}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: !isModelLoaded || !gazeAnalysisEnabled ? '#ccc' : '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: !isModelLoaded || !gazeAnalysisEnabled ? 'not-allowed' : 'pointer',
                    fontSize: '14px',
                    marginBottom: '10px',
                    display: 'block',
                    width: '100%',
                  }}
                >
                  {referenceSet ? 'Reference Set ✓' : 'Set Reference'}
                </button>

                <button
                  onClick={clearCache}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#dc3545',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    marginBottom: '15px',
                    display: 'block',
                    width: '100%',
                  }}
                >
                  Clear Model Cache
                </button>

                <div
                  style={{
                    display: 'flex',
                    gap: '10px',
                    flexDirection: 'column',
                  }}
                >
                  <label
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      cursor: 'pointer',
                    }}
                  >
                    <input
                      type='checkbox'
                      checked={gazeAnalysisEnabled}
                      onChange={(e) => handleToggleGazeAnalysis(e.target.checked)}
                    />
                    <strong>Enable Gaze Analysis</strong>
                  </label>

                  <label
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      cursor: 'pointer',
                      opacity: gazeAnalysisEnabled ? 1 : 0.6,
                    }}
                  >
                    <input
                      type='checkbox'
                      checked={showLandmarks}
                      onChange={(e) => setShowLandmarks(e.target.checked)}
                      disabled={!gazeAnalysisEnabled}
                    />
                    Show Landmarks
                  </label>

                  <label
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      cursor: 'pointer',
                      opacity: gazeAnalysisEnabled ? 1 : 0.6,
                    }}
                  >
                    <input
                      type='checkbox'
                      checked={showEngagementBar}
                      onChange={(e) => setShowEngagementBar(e.target.checked)}
                      disabled={!gazeAnalysisEnabled}
                    />
                    Show Engagement Bar
                  </label>
                </div>
              </div>

              <div
                style={{
                  padding: '15px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '5px',
                }}
              >
                <h3 style={{ margin: '0 0 10px 0' }}>Status: {statusMessage}</h3>

                {loadingProgress > 0 && loadingProgress < 100 && (
                  <div style={{ marginBottom: '10px' }}>
                    <div
                      style={{
                        width: '100%',
                        backgroundColor: '#e0e0e0',
                        borderRadius: '4px',
                        height: '8px',
                      }}
                    >
                      <div
                        style={{
                          width: `${loadingProgress}%`,
                          backgroundColor: '#007bff',
                          height: '100%',
                          borderRadius: '4px',
                          transition: 'width 0.3s ease',
                        }}
                      />
                    </div>
                    <small style={{ color: '#666' }}>
                      Loading model... {loadingProgress.toFixed(1)}%
                    </small>
                  </div>
                )}

                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '10px',
                    fontSize: '14px',
                  }}
                >
                  <div>
                    <strong>FPS:</strong>{' '}
                    <span
                      style={{
                        color: fps < 15 ? 'red' : fps < 25 ? 'orange' : 'green',
                      }}
                    >
                      {fps}
                    </span>
                  </div>

                  <div>
                    <strong>Model:</strong> {isModelLoaded ? '✓ Loaded' : '✗ Loading'}
                  </div>

                  {gazeAnalysisEnabled && (
                    <>
                      <div>
                        <strong>Engagement:</strong> {engagement.toFixed(1)}%
                      </div>

                      <div>
                        <strong>Looking:</strong>{' '}
                        <span style={{ color: isLookingAway ? 'red' : 'green' }}>
                          {isLookingAway ? 'Away' : 'At Screen'}
                        </span>
                      </div>
                    </>
                  )}
                </div>

                {multipleFacesDetected && (
                  <div
                    style={{
                      marginTop: '10px',
                      padding: '10px',
                      backgroundColor: '#e7f3ff',
                      borderRadius: '4px',
                      fontSize: '13px',
                      color: '#0056b3',
                    }}
                  >
                    <strong>Multiple Faces Detected</strong>
                    <br />
                    Please ensure only one person is in the frame.
                  </div>
                )}

                {!gazeAnalysisEnabled && (
                  <div
                    style={{
                      marginTop: '10px',
                      padding: '10px',
                      backgroundColor: '#e7f3ff',
                      borderRadius: '4px',
                      fontSize: '13px',
                      color: '#0056b3',
                    }}
                  >
                    <strong>Gaze Analysis Disabled</strong>
                    <br />
                    Enable to track engagement and attention levels.
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UserCamera;
