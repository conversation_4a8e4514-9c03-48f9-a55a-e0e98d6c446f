import { generateUniqueId } from '@/features/admin/jobs/create-job/utils/helper';
import { useProctoringStore } from '@/stores/proctoring-store';
import { useCallback } from 'react';

interface ViolationLoggerProps {
  onViolation?: (violation: ViolationLogger, violationCount: number) => void;
  onEnd?: (reason: string, violationCount: number) => void;
  maxViolations?: number;
}

const useViolationLogger = ({
  onViolation,
  onEnd: onExamEnd,
  maxViolations = 5,
}: ViolationLoggerProps) => {
  const { addViolation, logViolations, totalViolations } = useProctoringStore();

  const onInterviewEnd = useCallback(
    (reason: string) => {
      if (onExamEnd) {
        onExamEnd(reason, totalViolations);
      }
    },
    [onExamEnd]
  );

  const logViolation = useCallback(
    ({ type, severity, details }: Violation) => {
      const violation: ViolationLogger = {
        id: generateUniqueId(),
        type,
        severity,
        details,
        timestamp: Date.now(),
      };

      addViolation(violation);

      if (onViolation) {
        onViolation(violation, totalViolations);
      }

      if (totalViolations >= maxViolations) {
        onInterviewEnd('excessive_violations');
      }
    },
    [onViolation, maxViolations, onInterviewEnd]
  );

  return { logViolation, onInterviewEnd, violationCount: totalViolations, logList: logViolations };
};

export default useViolationLogger;
