import { FaceLandmarker, FilesetResolver } from '@mediapipe/tasks-vision';
import { useEffect, useRef, useState } from 'react';

// Model URL
const MODEL_URL =
  'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/latest/face_landmarker.task';

const WASM_URL = 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm';

export const useModelLoader = () => {
  const faceLandmarkerRef = useRef(null);
  const [isModelLoaded, setIsModelLoaded] = useState(false);
  const [statusMessage, setStatusMessage] = useState('Initializing...');
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [permissionDenied, setPermissionDenied] = useState(false);

  // Register service worker for caching
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('ServiceWorker registered:', registration);
        })
        .catch((error) => {
          console.log('ServiceWorker registration failed:', error);
        });
    }
  }, []);

  // --- Check if model is cached ---
  const checkCachedModels = async () => {
    if ('caches' in window) {
      const cache = await caches.open('mediapipe-models-v1');
      const cachedModel = await cache.match(MODEL_URL);
      return cachedModel !== undefined;
    }
    return false;
  };

  // --- Preload model with progress ---
  const preloadModel = async () => {
    try {
      const response = await fetch(MODEL_URL);
      const contentLength = response.headers.get('content-length');
      const total = contentLength ? parseInt(contentLength, 10) : 0;

      if (!response.body) throw new Error('No response body');

      const reader = response.body.getReader();
      let received = 0;
      const chunks = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        chunks.push(value);
        received += value.length;

        if (total) {
          const progress = Math.round((received / total) * 100);
          setLoadingProgress(progress);
          setStatusMessage(`Downloading model... ${progress}%`);
        }
      }

      if ('caches' in window) {
        const cache = await caches.open('mediapipe-models-v1');
        await cache.put(
          MODEL_URL,
          new Response(new Blob(chunks), {
            headers: { 'Content-Type': 'application/octet-stream' },
          })
        );
      }

      return true;
    } catch (error) {
      console.error('Failed to preload model:', error);
      return false;
    }
  };

  // --- Initialize FaceLandmarker ---
  useEffect(() => {
    const initialize = async () => {
      try {
        setStatusMessage('Checking cached models...');
        const isCached = await checkCachedModels();

        if (!isCached) {
          setStatusMessage('Model not cached. Downloading...');
          await preloadModel();
        } else {
          setStatusMessage('Using cached model...');
        }

        setStatusMessage('Loading MediaPipe WASM...');
        const vision = await FilesetResolver.forVisionTasks(WASM_URL);

        setStatusMessage('Creating face landmarker...');
        const faceLandmarker = await FaceLandmarker.createFromOptions(vision, {
          baseOptions: {
            modelAssetPath: MODEL_URL,
            delegate: 'GPU',
          },
          runningMode: 'VIDEO',
          outputFaceBlendshapes: true,
          outputFaceLandmarks: true,
          numFaces: 1,
        });

        faceLandmarkerRef.current = faceLandmarker;
        setIsModelLoaded(true);
        setStatusMessage('Model loaded successfully ✅');
      } catch (err) {
        console.error('Model init error:', err);
        setStatusMessage(`Error: ${err.message}`);
        if (err.name === 'NotAllowedError') setPermissionDenied(true);
      }
    };

    initialize();
  }, []);

  const clearCache = async () => {
    if ('caches' in window) {
      await caches.delete('mediapipe-models-v1');
      setStatusMessage('Cache cleared. Please refresh to re-download models.');
    }
  };

  return {
    faceLandmarkerRef,
    isModelLoaded,
    statusMessage,
    setStatusMessage,
    loadingProgress,
    permissionDenied,
    clearCache,
  };
};
