import { useEffect, useRef } from 'react';

const useTabSwitchMonitor = (logViolation: (violation: Violation) => void) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tabSwitchDetectedRef = useRef(false);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        tabSwitchDetectedRef.current = true;

        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        logViolation({
          type: 'tab_switch',
          severity: 'high',
          details: {
            action: 'tab_hidden',
            reason: 'Attempted to switch tabs or applications',
          },
        });

        setTimeout(() => {
          tabSwitchDetectedRef.current = false;
        }, 100);
      }
    };

    const handleWindowBlur = () => {
      if (!tabSwitchDetectedRef.current) {
        // Adding a small delay to allow tab switch to be detected first
        timeoutRef.current = setTimeout(() => {
          if (!tabSwitchDetectedRef.current) {
            logViolation({
              type: 'focus_loss',
              severity: 'medium',
              details: {
                action: 'window_blur',
                reason: 'Focus lost from the interview window',
              },
            });
          }
          timeoutRef.current = null;
        }, 50);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('blur', handleWindowBlur);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('blur', handleWindowBlur);

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [logViolation]);
};

export default useTabSwitchMonitor;
