import { useEffect } from 'react';

const useKeyboardPrevention = (logViolation: (violation: Violation) => void) => {
  useEffect(() => {
    const preventKeyboardShortcuts = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.meta<PERSON>ey) {
        const blockedKeys = ['c', 'v', 'a', 't', 'n', 'w', 'r', 's', 'p'];
        if (blockedKeys.includes(e.key.toLowerCase())) {
          e.preventDefault();
          logViolation({
            type: 'blocked_shortcut',
            severity: 'low',
            details: {
              action: 'shortcut_pressed',
              reason: `Attempted to use a blocked keyboard shortcut: Ctrl + ${e.key}`,
            },
          });
        }
      }

      if (e.key.startsWith('F') && e.key.length <= 3) {
        e.preventDefault();
        logViolation({
          type: 'blocked_function_key',
          severity: 'low',
          details: {
            action: 'function_key_pressed',
            reason: `Attempted to use a blocked function key: ${e.key}`,
          },
        });
      }

      if (e.altKey && e.key === 'Tab') {
        e.preventDefault();
        logViolation({
          type: 'alt_tab_attempt',
          severity: 'medium',
          details: {
            action: 'alt_tab',
            reason: 'Attempted to use the Alt + Tab shortcut',
          },
        });
      }
    };

    const preventRightClick = (e: MouseEvent) => {
      e.preventDefault();
      logViolation({
        type: 'right_click_attempt',
        severity: 'low',
        details: {
          action: 'right_click',
          reason: 'Attempted to use the right click',
        },
      });
    };

    document.addEventListener('keydown', preventKeyboardShortcuts);
    document.addEventListener('contextmenu', preventRightClick);

    return () => {
      document.removeEventListener('keydown', preventKeyboardShortcuts);
      document.removeEventListener('contextmenu', preventRightClick);
    };
  }, [logViolation]);
};

export default useKeyboardPrevention;
