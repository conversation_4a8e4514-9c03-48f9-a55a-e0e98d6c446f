import HookFormItem from '@/components/hook-form/HookFormItem';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui';
import { Form } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useUpdateProfileMutation, useUserProfileQuery } from '@/hooks/api/use-auth';
import { useAuthSlice } from '@/hooks/use-auth-slice';
import { getInitials, getUserRoleBadgeColor, snakeToTitleCase } from '@/utils/helper';
import { profileFormSchema, ProfileFormValues } from '@/validations/profileSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Edit2, User, X } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

const UpdateProfileForm = () => {
  const [isEditing, setIsEditing] = useState(false);

  const { user } = useAuthSlice();
  const { mutate: updateProfile, isPending } = useUpdateProfileMutation();

  // Call useUserProfileQuery only if user data is missing
  const shouldFetchProfile = !user?.user_name || !user?.email || !user?.role;
  useUserProfileQuery({ enabled: shouldFetchProfile });

  const defaultValues: Partial<ProfileFormValues> = {
    username: user?.user_name || '',
    email: user?.email || '',
    role: snakeToTitleCase(user?.role || 'Admin'),
  };

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues,
  });

  const handleSubmit = (data: ProfileFormValues) => {
    const payload = { name: data.username };

    updateProfile(payload, {
      onSuccess: () => {
        setIsEditing(false);
      },
    });
  };

  const currentUsername = form.watch('username');
  const hasUsernameChanged = currentUsername !== (user?.user_name || '');

  return (
    <>
      <Card>
        <CardContent className='pb-2'>
          <div className='flex items-center gap-4'>
            <Avatar className='h-16 w-16'>
              <AvatarImage src='/placeholder.svg?height=64&width=64' />
              <AvatarFallback className='bg-primary-50 text-primary border-primary-100 border text-xl font-semibold'>
                {getInitials(user?.user_name || '')}
              </AvatarFallback>
            </Avatar>
            <div className='flex-1'>
              <div className='mb-1 flex items-center gap-2'>
                <h2 className='text-xl font-semibold'>{user?.user_name}</h2>
                <Badge variant={getUserRoleBadgeColor(user?.role ?? '')} className='text-xs'>
                  {snakeToTitleCase(user?.role || 'Admin')}
                </Badge>
              </div>
              <p className='text-muted-foreground text-sm'>{user?.email}</p>
              <p className='text-muted-foreground mt-1 text-xs'>
                Activity Status: {user?.is_active ? 'Active' : 'Inactive'}
              </p>
            </div>
            <Button
              variant={isEditing ? 'destructive' : 'outline'}
              size='lg'
              onClick={isEditing ? () => setIsEditing(false) : () => setIsEditing(true)}
              className='gap-2'
              disabled={isPending}
            >
              {isEditing ? (
                <>
                  <X className='h-4 w-4' />
                  Cancel
                </>
              ) : (
                <>
                  <Edit2 className='h-4 w-4' />
                  Edit Profile
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className='flex items-center gap-3'>
            <div className='from-primary-400 to-primary-500 rounded-xl bg-gradient-to-b p-3'>
              <User className='size-6 text-white' />
            </div>
            <CardTitle>
              <p className='text-lg'>General Information</p>
              <p className='text-gray-light text-sm'>
                Update your personal information and contact details
              </p>
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className='space-y-6'>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
              <div className='grid grid-cols-2 gap-4'>
                <HookFormItem name='username' label='Full Name' isRequired>
                  <Input placeholder='previa' disabled={!isEditing} autoFocus />
                </HookFormItem>

                <HookFormItem name='email' label='Email Address'>
                  <Input readOnly className='text-muted-foreground cursor-not-allowed' />
                </HookFormItem>
              </div>

              <div>
                <HookFormItem name='role' label='Role'>
                  <Input readOnly className='text-muted-foreground cursor-not-allowed' />
                </HookFormItem>
                <p className='text-gray-light mt-1.5 ml-1 text-xs'>
                  Contact your administrator to change your role
                </p>
              </div>

              {isEditing && (
                <Button
                  type='submit'
                  disabled={!hasUsernameChanged}
                  className='w-32'
                  loading={isPending}
                >
                  Update profile
                </Button>
              )}
            </form>
          </Form>
        </CardContent>
      </Card>
    </>
  );
};
export default UpdateProfileForm;
