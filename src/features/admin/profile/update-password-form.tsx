import HookFormItem from '@/components/hook-form/HookFormItem';
import { PasswordInput } from '@/components/password-input';
import { Form } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useChangePasswordMutation } from '@/hooks/api/use-auth';
import { useAuthSlice } from '@/hooks/use-auth-slice';
import { cn } from '@/libs/utils';
import { changePasswordSchema, ChangePasswordValues } from '@/validations/passwordSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@radix-ui/react-dropdown-menu';
import { Shield } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

const UpdatePasswordForm = () => {
  const [showForm, setShowForm] = useState(false);

  const { user } = useAuthSlice();
  const { mutateAsync: changePassword, isPending } = useChangePasswordMutation();

  const form = useForm<ChangePasswordValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const handleSubmit = async (data: ChangePasswordValues) => {
    if (!user?.id) return;

    const payload = {
      old_password: data.currentPassword,
      new_password: data.newPassword,
    };

    changePassword(
      {
        id: user.id.toString(),
        payload,
      },
      {
        onSuccess: () => {
          form.reset();
          setShowForm(false);
        },
      }
    );
  };

  const handleCancel = () => {
    form.reset();
    setShowForm(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center gap-3'>
          <div className='from-primary-400 to-primary-500 rounded-xl bg-gradient-to-b p-3'>
            <Shield className='size-6 text-white' />
          </div>
          <CardTitle>
            <p className='text-lg'>Security & Privacy</p>
            <p className='text-gray-light text-sm'>Manage your password and security preferences</p>
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className='space-y-6'>
        <div className='space-y-4'>
          <div className='bg-custom-white flex items-center justify-between rounded-lg border p-6'>
            {showForm ? (
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)} className='w-full space-y-6'>
                  <HookFormItem
                    name='currentPassword'
                    label='Current Password'
                    isRequired
                    className='w-full'
                  >
                    <PasswordInput placeholder='Enter current password' className='w-full' />
                  </HookFormItem>

                  <HookFormItem name='newPassword' label='New Password' isRequired>
                    <PasswordInput placeholder='Enter new password' />
                  </HookFormItem>

                  <HookFormItem name='confirmPassword' label='Confirm New Password' isRequired>
                    <PasswordInput placeholder='Confirm new password' />
                  </HookFormItem>

                  <div className='flex gap-3'>
                    <Button type='submit' disabled={isPending}>
                      Update Password
                    </Button>
                    <Button type='button' variant='outline' onClick={handleCancel}>
                      Cancel
                    </Button>
                  </div>
                </form>
              </Form>
            ) : (
              <div className='max-w-md flex-1'>
                <div className='mb-2.5 flex items-center gap-2'>
                  <Label className='text-sm font-medium'>Password</Label>
                </div>
                <div className='flex items-center gap-2'>
                  <Input type='password' value='••••••••••' disabled />
                </div>
              </div>
            )}

            <Button
              variant='outline'
              onClick={() => setShowForm(true)}
              className={cn('h-9', {
                hidden: showForm,
              })}
            >
              Change Password
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
export default UpdatePasswordForm;
