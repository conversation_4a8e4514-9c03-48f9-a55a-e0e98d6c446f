export const generateUniqueId = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

export const calculateTotalDuration = (personas: InterviewPersona[]): string => {
  const totalMinutes = personas.reduce((sum, persona) => sum + parseInt(persona.duration), 0);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  if (hours > 0 && minutes > 0) return `${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h`;
  return `${minutes}m`;
};

export const formatPersonaOptions = (
  personas: ExtendedPersona[]
): { label: string; value: string }[] => {
  if (!personas || personas.length === 0) return [];

  return personas.map((persona) => ({
    label: persona.title,
    value: persona.title,
  }));
};
