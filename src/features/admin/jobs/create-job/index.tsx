import { useJobSteps } from './hooks/use-job-step';
import { GradientCircle } from '@/components/shared/GradientCircle';
import { PageHeader } from '@/components/shared/PageHeader';
import { Button } from '@/components/ui';
import {
  InterviewAgentStep,
  JobDescriptionStep,
  PreviewStep,
  ProgressBar,
} from '@/features/admin/jobs/create-job/components';
import { useLocation } from '@tanstack/react-router';
import { AnimatePresence, motion } from 'framer-motion';

const steps = [
  { id: 1, title: 'Job description' },
  { id: 2, title: 'Interview agent' },
  { id: 3, title: 'Preview & Publish' },
];

export default function CreateJobPage() {
  const { pathname } = useLocation();

  const {
    initialized,
    draftJobData,
    isDraftJobLoading,
    draftJobId,
    showEditor,
    setShowEditor,
    currentStep,
    handleBack,
    handleNextStep,
    stepFormRef,
  } = useJobSteps();

  const getPageMode = () => {
    if (pathname.includes('edit')) {
      return 'Edit';
    }
    return 'Create';
  };

  const breadcrumbsItem = [
    { label: 'Home', href: '/admin/dashboard' },
    { label: 'Job list', href: '/admin/jobs' },
    { label: getPageMode() + ' a job post' },
  ];

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <JobDescriptionStep
            ref={stepFormRef}
            draftJobId={draftJobId}
            draftJobData={draftJobData}
            isDraftJobLoading={isDraftJobLoading || !initialized}
            showEditor={showEditor}
            setShowEditor={setShowEditor}
          />
        );
      case 2:
        return <InterviewAgentStep ref={stepFormRef} />;
      case 3:
        return <PreviewStep />;
      default:
        return (
          <JobDescriptionStep
            ref={stepFormRef}
            draftJobId={draftJobId}
            draftJobData={draftJobData}
            isDraftJobLoading={isDraftJobLoading || !initialized}
            showEditor={showEditor}
            setShowEditor={setShowEditor}
          />
        );
    }
  };

  return (
    <div className='dark:bg-background bg-custom-white flex h-full flex-col'>
      <PageHeader
        title={getPageMode() + ' Job'}
        showNavigation={true}
        breadcrumbs={breadcrumbsItem}
      />

      <div className='flex flex-1 overflow-y-auto px-6'>
        <div className='bg-card mx-auto mb-4 min-h-max w-full rounded-2xl border'>
          {/* Header */}
          <div className='border-b-strock flex items-center justify-between gap-4 rounded-t-2xl border-b px-6 py-2'>
            <div className='flex items-center gap-3'>
              {/* <ConfirmationModal
                title='Reset Draft'
                description='Are you sure you want to reset your draft? All your progress will be lost.'
                onConfirm={async () => {
                  resetForm();
                  clearDraftJobId();
                  stepFormRef.current?.triggerResetForm();
                  window.location.reload();
                }}
              >
                <Button size={'icon'} variant={'secondary'}>
                  <XIcon />
                </Button>
              </ConfirmationModal> */}
              <h1 className='text-2xl font-bold'>{getPageMode() + ' Job'}</h1>
            </div>
            <ProgressBar currentStep={currentStep} steps={steps} />
            <div className='flex'>
              {currentStep !== 1 || showEditor ? (
                <Button className='mr-2' variant='secondary' onClick={handleBack} size={'lg'}>
                  Back
                </Button>
              ) : null}

              {currentStep < 3 && (
                <Button className='' variant='default' onClick={handleNextStep} size={'lg'}>
                  Next
                </Button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className='relative isolate overflow-hidden p-6'>
            <div className='pointer-events-none absolute inset-0 -z-10'>
              <GradientCircle className='-top-120 left-1/2 -translate-x-1/2 !opacity-10' />
            </div>
            <AnimatePresence mode='wait'>
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className='z-30'
              >
                {renderStep()}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}
