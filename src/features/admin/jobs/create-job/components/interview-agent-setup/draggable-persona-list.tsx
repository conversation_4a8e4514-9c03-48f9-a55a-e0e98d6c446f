'use client';

import { DraggablePersonaCard } from './draggable-persona-card';
import { AnimatePresence, Reorder } from 'framer-motion';

interface DraggableListProps {
  personas: InterviewPersona[];
  setPersonas: React.Dispatch<React.SetStateAction<InterviewPersona[]>>;
}

export function DraggablePersonaList({ personas, setPersonas }: DraggableListProps) {
  return (
    <Reorder.Group axis='y' values={personas} onReorder={setPersonas} className='space-y-6'>
      <AnimatePresence>
        {personas.map((persona) => (
          <DraggablePersonaCard key={persona.id} persona={persona} setPersonas={setPersonas} />
        ))}
      </AnimatePresence>
    </Reorder.Group>
  );
}
