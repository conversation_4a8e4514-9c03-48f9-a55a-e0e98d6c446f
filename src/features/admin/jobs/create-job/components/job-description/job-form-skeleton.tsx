export function JobFormSkeleton() {
  return (
    <div className='bg-card mx-auto w-full p-6'>
      <div className='space-y-6'>
        <div>
          <div className='mb-2 h-4 w-20 animate-pulse rounded bg-gray-200'></div>
          <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
        </div>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
          <div>
            <div className='mb-2 h-4 w-32 animate-pulse rounded bg-gray-200'></div>
            <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
          </div>

          <div>
            <div className='mb-2 h-4 w-16 animate-pulse rounded bg-gray-200'></div>
            <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
          </div>
        </div>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
          <div>
            <div className='mb-2 h-4 w-36 animate-pulse rounded bg-gray-200'></div>
            <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
          </div>
          <div>
            <div className='mb-2 h-4 w-40 animate-pulse rounded bg-gray-200'></div>
            <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
          </div>
        </div>

        <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
          <div>
            <div className='mb-2 h-4 w-24 animate-pulse rounded bg-gray-200'></div>
            <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
          </div>

          <div>
            <div className='mb-2 h-4 w-24 animate-pulse rounded bg-gray-200'></div>
            <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
          </div>
        </div>

        <div className='rounded-lg bg-gray-50 p-4'>
          <div className='mb-2 flex items-center gap-2'>
            <div className='h-4 w-56 animate-pulse rounded bg-gray-200'></div>
            <div className='h-4 w-4 animate-pulse rounded-full bg-gray-200'></div>
          </div>
        </div>

        <div className='grid grid-cols-1 gap-4 pt-4 md:grid-cols-2'>
          <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
          <div className='h-12 animate-pulse rounded-md bg-gray-200'></div>
        </div>
      </div>
    </div>
  );
}
