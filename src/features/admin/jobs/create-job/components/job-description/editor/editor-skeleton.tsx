import React from 'react';

export const EditorSkeleton: React.FC = () => {
  return (
    <div className='min-h-[400px] animate-pulse'>
      {/* Toolbar skeleton */}
      <div className='mb-2 flex space-x-2 border-b border-gray-200 p-2'>
        <div className='h-8 w-20 rounded bg-gray-200'></div>
        <div className='h-8 w-16 rounded bg-gray-200'></div>
        <div className='h-8 w-12 rounded bg-gray-200'></div>
        <div className='h-8 w-12 rounded bg-gray-200'></div>
        <div className='h-8 w-12 rounded bg-gray-200'></div>
      </div>

      {/* Content area skeleton */}
      <div className='space-y-4 p-4' style={{ height: '350px' }}>
        <div className='h-6 w-3/4 rounded bg-gray-200'></div>
        <div className='h-4 w-full rounded bg-gray-200'></div>
        <div className='h-4 w-5/6 rounded bg-gray-200'></div>
        <div className='h-4 w-4/5 rounded bg-gray-200'></div>

        <div className='mt-6 space-y-2'>
          <div className='h-5 w-1/3 rounded bg-gray-200'></div>
          <div className='h-4 w-full rounded bg-gray-200'></div>
          <div className='h-4 w-11/12 rounded bg-gray-200'></div>
          <div className='h-4 w-5/6 rounded bg-gray-200'></div>
        </div>

        <div className='mt-6 space-y-2'>
          <div className='h-5 w-1/4 rounded bg-gray-200'></div>
          <div className='h-4 w-full rounded bg-gray-200'></div>
          <div className='h-4 w-4/5 rounded bg-gray-200'></div>
        </div>
      </div>
    </div>
  );
};
