import { useAiDescription } from '../../../hooks/use-ai-description';
import { AI, EditorMode } from '../../../utils/constant';
import { EditorSkeleton } from './editor-skeleton';
import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';
import { useJobStore } from '@/features/admin/jobs/create-job/hooks/use-job-store';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import ReactQuill from 'react-quill-new';
import 'react-quill-new/dist/quill.snow.css';
import { toast } from 'sonner';

interface JobDescriptionEditorProps {
  mode: EditorMode;
  jobId?: string;
  onRegenerateAI?: () => void;
  isGenerating?: boolean;
}

export function JobDescriptionEditor({
  mode,
  jobId,
  onRegenerateAI,
  isGenerating,
}: JobDescriptionEditorProps) {
  const { nextStep, updateFormData, formData } = useJobStore();

  const { draftJobId } = useJobCreationStore();
  const { description: hookDescription, saveDescription } = useAiDescription(
    jobId || draftJobId
  );
  const [description, setDescription] = useState(formData?.description || '');

  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ['align'],
      [{ color: [] }, { background: [] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ list: 'ordered' }, { list: 'bullet' }],
      ['link', 'code-block'],
    ],
  };

  const formats = [
    'header',
    'bold',
    'italic',
    'underline',
    'strike',
    'list',
    'link',
    'code-block',
    'align',
    'direction',
    'color',
    'background',
  ];

  useEffect(() => {
    if (hookDescription) setDescription(hookDescription);
  }, [hookDescription]);

  const handleSaveAndNext = async () => {
    if (!description?.trim()) {
      toast.error('Please add a job description before proceeding');
      return;
    }

    if (jobId || draftJobId) {
      updateFormData({
        description: description,
      });
      const saved = await saveDescription(description);
      if (!saved) {
        return;
      }
    }

    nextStep();
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className='mx-auto max-w-2xl'
    >
      <Card className='border-none bg-transparent shadow-none'>
        <CardHeader className='text-center'>
          <CardTitle className='text-xl font-bold'>Define The Role</CardTitle>
          <CardDescription className='text-md mx-auto max-w-xl text-neutral-500'>
            {mode === AI
              ? 'Create your job post your way. Write a prompt to let AI generate it for you, or build it manually by filling out the form.'
              : 'Create your job description manually by writing the content yourself.'}
          </CardDescription>
        </CardHeader>

        <CardContent className='space-y-6 rounded-2xl border border-neutral-200 bg-white p-10'>
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-semibold text-black'>Description</h3>
              {/* {mode === 'ai' && (
                <Button onClick={onRegenerateAI} loading={isGenerating} disabled={isGenerating}>
                  {isGenerating ? 'Generating...' : 'Rewrite with AI'}
                </Button>
              )} */}
            </div>

            <div className='min-h-110'>
              {isGenerating ? (
                <EditorSkeleton />
              ) : (
                <ReactQuill
                  theme='snow'
                  value={description}
                  onChange={setDescription}
                  modules={modules}
                  formats={formats}
                  placeholder='Write your job description here...'
                  style={{ height: '400px' }}
                />
              )}
            </div>
          </div>

          <div className='flex flex-col items-center justify-center gap-3 pt-4 md:flex-row'>
            <Button
              variant='secondary'
              onClick={onRegenerateAI}
              className='w-1/2'
              disabled={isGenerating}
            >
              {isGenerating ? 'Generating...' : 'Rewrite with AI'}
            </Button>
            <Button
              onClick={handleSaveAndNext}
              className='w-1/2'
              disabled={!description.trim() || isGenerating}
            >
              Save & Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
