import { JOB_STATUS } from '../../../job-list/utils/constant';
import { useAiDescription } from '../../hooks/use-ai-description';
import { AI, EditorMode, MANUAL } from '../../utils/constant';
import { CardHeaderSkeleton } from './card-header-skeleton';
import { JobDescriptionEditor } from './editor/job-description-editor';
import { EligibilitySettings } from './eligibility-settings';
import { JobFormSkeleton } from './job-form-skeleton';
import HookFormItem from '@/components/hook-form/HookFormItem';
import SimpleSelect from '@/components/select/simple-selector';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  DatePicker,
  Form,
  Input,
} from '@/components/ui';
import { useJobStore } from '@/features/admin/jobs/create-job/hooks/use-job-store';
import { useCreateJobMutation, useJobRolesQuery, useUpdateJobMutation } from '@/hooks/api/use-job';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { MAX_PAGE_SIZE } from '@/utils/constants';
import { getDateToISOString } from '@/utils/helper';
import jobInfoSchema, { type JobInfoType } from '@/validations/jobSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

interface JobDescriptionStepProps {
  draftJobId: string | null;
  draftJobData: JobInformationResponse | undefined;
  isDraftJobLoading: boolean;
  showEditor: boolean;
  setShowEditor: (value: boolean) => void;
}

export const JobDescriptionStep = forwardRef<
  { triggerValidation: () => Promise<boolean> },
  JobDescriptionStepProps
>(({ draftJobId, draftJobData, isDraftJobLoading, showEditor, setShowEditor }, ref) => {
  const allLabelClsName = '!text-foreground font-semibold tracking-wide';
  const { updateFormData, formData } = useJobStore();
  const { setDraftJobId } = useJobCreationStore();

  const { mutate: createJobInformation, isPending: isCreating } = useCreateJobMutation();
  const { mutate: updateJobInformation, isPending: isUpdating } = useUpdateJobMutation();

  const [jobId, setJobId] = useState<string | undefined>(undefined);
  const [editorMode, setEditorMode] = useState<EditorMode>(MANUAL);
  const [eligibilityData, setEligibilityData] = useState<{
    enabled: boolean;
    initialCriteria: string | null;
  }>({
    enabled: false,
    initialCriteria: null,
  });

  const { data: jobRolesData, isLoading: isJobRolesLoading } = useJobRolesQuery({
    limit: MAX_PAGE_SIZE,
    offset: 0,
  });

  const { triggerGenerate, isGenerating } = useAiDescription(jobId || draftJobId);

  // Transform job roles data for Select component
  const jobRoleOptions = useMemo(() => {
    if (!jobRolesData) return [];
    return (
      jobRolesData.items?.map((role: JobRole) => ({
        label: role.name,
        value: role.id,
      })) || []
    );
  }, [jobRolesData]);

  const form = useForm<JobInfoType>({
    resolver: zodResolver(jobInfoSchema),
    defaultValues: {
      title: '',
      description: '',
      job_role_id: '',
      min_exp: '',
      max_exp: '',
      location: '',
    },
  });

  const isProcessing = isCreating || isUpdating;
  const isDraftMode = !!draftJobData;

  useEffect(() => {
    if (draftJobData && !isDraftJobLoading && jobRoleOptions.length > 0 && !isJobRolesLoading) {
      const applicationStartDate = formData.application_start_date
        ? new Date(formData.application_start_date)
        : undefined;
      const applicationEndDate = formData.application_end_date
        ? new Date(formData.application_end_date)
        : undefined;

      const jobFormData = {
        title: formData.title || '',
        description: formData.description || '',
        job_role_id: formData.job_role_id || '',
        location: formData.location || '',
        min_exp: formData.min_exp?.toString() || '',
        max_exp: formData.max_exp?.toString() || '',
        application_start_date: applicationStartDate,
        application_end_date: applicationEndDate,
        initial_filter_criteria: formData.initial_filter_criteria || '',
      };

      form.reset(jobFormData);
      // resetFormWithData(form, draftJobData);

      setEligibilityData({
        enabled: Boolean(formData.initial_filter_criteria?.trim()),
        initialCriteria: formData.initial_filter_criteria || '',
      });

      setJobId(draftJobData.id);

      updateFormData(jobFormData);

    }
  }, [draftJobData, isDraftJobLoading, isJobRolesLoading, jobRoleOptions, showEditor]);

  useImperativeHandle(ref, () => ({
    triggerValidation: async () => {
      return await form.trigger();
    },
    triggerResetForm: () => {
      form.reset();
      setShowEditor(false);
    },
  }));
  const handleGenerateWithAI = async (newJobId?: string) => {
    triggerGenerate(newJobId || jobId || draftJobId);
    setShowEditor(true);
  };

  const onSubmit = (data: JobInfoType, submitType: EditorMode) => {
    const jobData: JobInformation = {
      ...data,
      min_exp: parseInt(data.min_exp),
      max_exp: parseInt(data.max_exp),
      location: data.location || 'Dhaka',
      application_start_date: getDateToISOString(data.application_start_date),
      application_end_date: getDateToISOString(data.application_end_date),
      status: JOB_STATUS.DRAFT,
      initial_filter_criteria: eligibilityData.enabled ? eligibilityData.initialCriteria : '',
    };

    updateFormData(jobData);
    setEditorMode(submitType);

    const handleSuccess = async (response: JobInformationResponse) => {
      const updatedJobData = {
        ...jobData,
        id: response?.id || jobId,
      };

      // Reset the form with the updated data
      setJobId(updatedJobData.id);
      setDraftJobId(updatedJobData.id || draftJobId);

      if (submitType === AI) {
        await handleGenerateWithAI(updatedJobData.id);
      }

      setShowEditor(true);
    };

    // Use update if we have a draft job, otherwise create new

    if (draftJobId && jobId) {
      updateJobInformation(
        { id: jobId, payload: jobData },
        {
          onSuccess: handleSuccess,
        }
      );
    } else {
      createJobInformation(jobData, {
        onSuccess: handleSuccess,
      });
    }
  };

  const handleEligibilityChange = (data: { enabled: boolean; initialCriteria: string | null }) => {
    setEligibilityData(data);
  };
  console.log({ showEditor, isGenerating });

  if (showEditor || isGenerating) {
    return (
      <JobDescriptionEditor
        mode={editorMode}
        jobId={jobId}
        onRegenerateAI={() => triggerGenerate(jobId || draftJobId)}
        isGenerating={isGenerating}
      />
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
      className='mx-auto max-w-2xl'
    >
      <Card className='border-none bg-transparent shadow-none'>
        <CardHeader className='text-center'>
          {draftJobId && isDraftJobLoading ? (
            <CardHeaderSkeleton />
          ) : (
            <>
              <CardTitle className='text-xl font-bold'>
                {isDraftMode ? 'Edit Draft Job' : 'Define the Role'}
              </CardTitle>
              <CardDescription className='text-md text-muted-foreground mx-auto max-w-xl'>
                {isDraftMode
                  ? 'Continue editing your draft job or update the description with AI'
                  : 'Create your job post your way. Write a prompt to let AI generate it for you, or build it manually by writing by your own'}
              </CardDescription>
            </>
          )}
        </CardHeader>

        {draftJobId && isDraftJobLoading ? (
          <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className='max-w-2xl'>
            <JobFormSkeleton />
          </motion.div>
        ) : (
          <Form {...form}>
            <form>
              <CardContent className='bg-card z-[9999] space-y-6 rounded-2xl border border-neutral-200 p-10'>
                <div className='space-y-2'>
                  <HookFormItem
                    name='title'
                    label='Job title'
                    labelClassName={allLabelClsName}
                    isRequired
                  >
                    <Input placeholder='e.g. Senior React Developer' />
                  </HookFormItem>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <HookFormItem
                      name='job_role_id'
                      label='Position Applied For'
                      labelClassName={allLabelClsName}
                      isRequired
                    >
                      <SimpleSelect
                        options={jobRoleOptions}
                        placeholder='Select job position'
                        name='job_role_id'
                        // value={form.watch('job_role_id')}
                        defaultValue={draftJobData?.job_role?.id}
                      />
                    </HookFormItem>
                  </div>

                  <div className='space-y-2'>
                    <HookFormItem name='location' label='Location' labelClassName={allLabelClsName}>
                      <Input placeholder='Enter location' name='location' />
                    </HookFormItem>
                  </div>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <HookFormItem
                      name='min_exp'
                      label='Min Experience (Years)'
                      labelClassName={allLabelClsName}
                      isRequired
                    >
                      <Input />
                    </HookFormItem>
                  </div>

                  <div className='space-y-2'>
                    <HookFormItem
                      name='max_exp'
                      label='Max Experience (Years)'
                      labelClassName={allLabelClsName}
                      isRequired
                    >
                      <Input />
                    </HookFormItem>
                  </div>
                </div>

                {/* Skills Section if needed */}
                {/* <div className='space-y-2'>
                  <HookFormItem name='skills' label='Skills' labelClassName={allLabelClsName}>
                    <MultiSelect options={skills} placeholder='Select skills' />
                  </HookFormItem>
                </div> */}

                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <HookFormItem
                      name='application_start_date'
                      label='Starting date'
                      className='flex flex-col'
                      labelClassName={allLabelClsName}
                      isRequired
                    >
                      <DatePicker
                        key='start-date'
                        id='start-date'
                        defaultDate={form.watch('application_start_date')}
                        onChange={(date) => {
                          if (date) form.setValue('application_start_date', date);
                        }}
                      />
                    </HookFormItem>
                  </div>

                  <div className='space-y-2'>
                    <HookFormItem
                      name='application_end_date'
                      label='Ending date'
                      className='flex flex-col'
                      labelClassName={allLabelClsName}
                      isRequired
                    >
                      <DatePicker
                        key='end-date'
                        id='end-date'
                        defaultDate={form.watch('application_end_date')}
                        onChange={(date) => {
                          if (date) form.setValue('application_end_date', date);
                        }}
                      />
                    </HookFormItem>
                  </div>
                </div>

                <EligibilitySettings
                  onEligibilityChange={handleEligibilityChange}
                  initialData={eligibilityData}
                />

                <div className='flex w-full gap-3'>
                  <div className='relative inline-block w-1/2'>
                    <div className='absolute -inset-0.5 overflow-hidden rounded-lg'>
                      <div className='animate-border-sweep absolute inset-0 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-80'></div>
                    </div>

                    <Button
                      type='submit'
                      name='submitTypeAi'
                      value={AI}
                      disabled={isProcessing}
                      onClick={form.handleSubmit((data) => onSubmit(data, AI))}
                      className='bg-logo-gradient relative w-full min-w-[250px] overflow-hidden border-0 transition-all before:absolute before:top-0 before:right-0 before:h-full before:w-12 before:translate-x-12 before:rotate-6 before:bg-white before:opacity-10 before:duration-700 hover:shadow-lg hover:shadow-purple-500/30 hover:before:-translate-x-80'
                    >
                      <span className='relative z-10'>Create Description with AI</span>
                    </Button>
                  </div>

                  <Button
                    variant='secondary'
                    type='submit'
                    name='submitTypeManual'
                    value={MANUAL}
                    onClick={form.handleSubmit((data) => onSubmit(data, MANUAL))}
                    disabled={isProcessing}
                    className='w-1/2'
                  >
                    Create manually
                  </Button>
                </div>
              </CardContent>
            </form>
          </Form>
        )}
      </Card>
    </motion.div>
  );
});
