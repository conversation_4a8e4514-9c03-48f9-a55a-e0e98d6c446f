import { useJobStore } from './use-job-store';
import {
  useGenerateJobDescription as useGenerateJobDescriptionQuery,
  useUpdateJobMutation,
} from '@/hooks/api/use-job';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export const useAiDescription = (jobId?: string | null) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const { updateFormData, formData } = useJobStore();
  const updateJobMutation = useUpdateJobMutation();

  const { data, isLoading, error, refetch } = useGenerateJobDescriptionQuery(jobId || '', {
    enabled: isEnabled && !!jobId,
  });

  const parseHtmlContent = (rawContent: string): string => {
    if (!rawContent) return '';

    // Remove markdown code block syntax and clean up the content
    const cleanedContent = rawContent
      .replace(/```html\n?/g, '') // Remove opening ```html
      .replace(/```\n?/g, '') // Remove closing ```
      .replace(/\\n/g, '\n') // Replace escaped newlines with actual newlines
      .replace(/\n/g, '') // Remove actual newlines to let HTML handle formatting
      .trim();

    return cleanedContent;
  };

  const parsedDescription = data ? parseHtmlContent(data) : null;

  useEffect(() => {
    if (parsedDescription) {
      updateFormData({ description: parsedDescription });
    }
  }, [parsedDescription, updateFormData]);

  const triggerGenerate = async (idToUse?: string | null) => {
    if (!idToUse && !jobId) {
      toast.error('Job needs to be created to generate description');
      return null;
    }

    setIsEnabled(true);
    try {
      const result = await refetch();
      if (result.data) {
        const description = parseHtmlContent(result.data);
        updateFormData({ description });
        return description;
      } else {
        toast.error('Failed to generate job description');
        return null;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An error occurred while generating job description';
      toast.error(errorMessage);
      return null;
    } finally {
      setIsEnabled(false);
    }
  };

  const saveDescription = async (
    description: string,
    additionalPayload?: Partial<JobInformation>
  ) => {
    if (!jobId) {
      toast.error('Job ID is required for saving description');
      return false;
    }

    try {
      await updateJobMutation.mutateAsync({
        id: jobId,
        payload: {
          description,
          ...additionalPayload,
        } as JobInformation,
      });
      updateFormData({
        description,
      });
      return true;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'An error occurred while saving job description';
      toast.error(errorMessage);
      return false;
    }
  };

  return {
    description: formData.description,
    triggerGenerate,
    saveDescription,
    isSaving: updateJobMutation.isPending,
    isGenerating: isLoading || isEnabled,
    error,
  };
};
