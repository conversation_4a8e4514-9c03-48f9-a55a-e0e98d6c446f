import { calculateTotalDuration } from '../utils/helper';
import { useJobStore } from './use-job-store';
import { useJobByIdQuery } from '@/hooks/api/use-job';
import { useGetJobPersonasQuery } from '@/hooks/api/use-persona';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { useEffect, useRef, useState } from 'react';

export function useJobSteps() {
  const { draftJobId, setDraftJobId } = useJobCreationStore();
  const { currentStep, setCurrentStep, nextStep, prevStep, updateFormData } = useJobStore();
  const [showEditor, setShowEditor] = useState(false);
  const [initialized, setInitialized] = useState(false);

  const stepFormRef = useRef<{
    triggerValidation: () => Promise<boolean>;
    triggerResetForm: () => void;
  }>(null);

  const { data: draftJobData, isLoading: isDraftJobLoading } = useJobByIdQuery(draftJobId || '', {
    enabled: !!draftJobId,
  });

  const { data: jobPersonas } = useGetJobPersonasQuery(draftJobId || '', {
    enabled: !!draftJobId,
  });

  useEffect(() => {
    if (!draftJobData || isDraftJobLoading) return;

    if (draftJobData) {
      if (draftJobData.has_description && !draftJobData.has_persona) {
        setCurrentStep(2);
        setShowEditor(true);
      } else if (draftJobData.has_persona) {
        setCurrentStep(3);
      } else {
        setCurrentStep(1);
        setShowEditor(true);
      }
      // Sync store with draft job data
      updateFormData({
        title: draftJobData.title || '',
        description: draftJobData.description || '',
        location: draftJobData.location || '',
        job_role_id: draftJobData.job_role?.id || '',
        min_exp: draftJobData.min_exp?.toString() || '',
        max_exp: draftJobData.max_exp?.toString() || '',
        application_start_date: draftJobData.application_start_date || '',
        application_end_date: draftJobData.application_end_date || '',
        initial_filter_criteria: draftJobData.initial_filter_criteria || '',
      });

      if (draftJobData && !isDraftJobLoading && jobPersonas && jobPersonas?.length > 0) {
        updateFormData({
          interviewPersonas: jobPersonas.map((persona) => ({
            id: persona.persona_id,
            persona: persona.persona_title,
            duration: persona.time_duration.toString(),
          })),
          totalPersonas: jobPersonas.length,
          totalDuration: calculateTotalDuration(
            jobPersonas.map((persona) => ({
              id: persona.persona_id,
              persona: persona.persona_title,
              duration: persona.time_duration.toString(),
            }))
          ),
        });
      }

      setDraftJobId(draftJobData.id);
    }
    setInitialized(true);
  }, [draftJobData, isDraftJobLoading, jobPersonas]);

  const handleNextStep = async () => {
    const isValid = await stepFormRef.current?.triggerValidation();
    if (currentStep === 1) {
      if (isValid && !showEditor) {
        setShowEditor(true);
      } else {
        nextStep();
      }
    } else {
      nextStep();
    }
  };

  const handleBack = () => {
    if (currentStep === 1 && showEditor) {
      setShowEditor(false);
    } else {
      prevStep();
    }
  };

  return {
    initialized,
    draftJobId,
    draftJobData,
    isDraftJobLoading,
    showEditor,
    setShowEditor,
    stepFormRef,
    currentStep,
    handleNextStep,
    handleBack,
  };
}
