import { JobPreviewModal } from './job-preview-modal';
import { SimpleDropdown } from '@/components/ui';
import { jobKeys, useUpdateJobMutation } from '@/hooks/api/use-job';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { JOB_STATUS } from '@/utils/constants';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { Check, Eye, SquarePen, Trash2, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useJobStore } from '../../create-job/hooks/use-job-store';

interface Props {
  jobData: JobDetailsAdmin;
}

export const JobActionDropdown = ({ jobData }: Props) => {
  const navigate = useNavigate();
  const { setDraftJobId } = useJobCreationStore();
  const {setCurrentStep} = useJobStore();

  const [showPreviewModal, setShowPreviewModal] = useState(false);

  const queryClient = useQueryClient();

  const { mutate: updateJobStatus, isPending: isUpdating } = useUpdateJobMutation();

  const handleEditJobPost = () => {
    setDraftJobId(jobData.id);
    setCurrentStep(1);
    navigate({ to: '/admin/jobs/edit' });
  };

  const handleMarkAsInactive = () => {
    const currentJobStatus = jobData?.status;
    const newJobStatus =
      currentJobStatus === JOB_STATUS.ACTIVE ? JOB_STATUS.INACTIVE : JOB_STATUS.ACTIVE;

    updateJobStatus(
      { id: jobData.id, payload: { status: newJobStatus } },
      {
        onSuccess: () => {
          toast.success(`Job marked as ${newJobStatus}`);
          queryClient.invalidateQueries({ queryKey: jobKeys.adminDetails(jobData.id) });
        },
      }
    );
  };

  return (
    <>
      <SimpleDropdown
        triggerClassName='bg-custom-white border border-strock size-10 focus-visible:border-strock'
        items={[
          {
            label: 'Edit Job Post',
            onClick: handleEditJobPost,
            icon: <SquarePen />,
          },
          {
            label: `Mark as ${jobData?.status === JOB_STATUS.ACTIVE ? 'Inactive' : 'Active'}`,
            onClick: handleMarkAsInactive,
            icon: jobData?.status === JOB_STATUS.ACTIVE ? <X /> : <Check />,
            disabled: isUpdating,
          },
          {
            label: 'Preview',
            onClick: () => setShowPreviewModal(true),
            icon: <Eye />,
          },
          { label: 'Remove', onClick: () => {}, icon: <Trash2 />, disabled: true },
        ]}
      />

      {showPreviewModal && (
        <JobPreviewModal jobData={jobData} open={showPreviewModal} setOpen={setShowPreviewModal} />
      )}
    </>
  );
};
