import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import JobInformation from '@/features/candidate/application-form/components/job-information';

interface Props {
  jobData: JobDetailsAdmin;
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

export const JobPreviewModal = ({ open, setOpen, jobData }: Props) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className='h-[80vh] overflow-y-auto sm:max-w-4xl'>
        <DialogHeader>
          <DialogTitle>Job Preview</DialogTitle>
          <DialogDescription>This is how the job post will look to candidates.</DialogDescription>
        </DialogHeader>

        <JobInformation
          job={{
            title: jobData.title,
            description: jobData.description,
            location: jobData.location,
            min_exp: jobData.min_exp,
            max_exp: jobData.max_exp,
          }}
        />
      </DialogContent>
    </Dialog>
  );
};
