import { DetailsViewer } from './details-viewer';
import { InterviewPersonas } from './interview-personas';
import { JobApplicantsTable } from './job-applicants-table';
import { DataTabs } from '@/components/data-tabs';

interface Props {
  jobDescription: string;
  filterCriteria: string;
}

export const JobTabContents = ({ jobDescription, filterCriteria }: Props) => {
  const tabConfig: TabItem[] = [
    {
      label: 'Job Description',
      value: 'job-description',
      content: () => <DetailsViewer description={jobDescription} filterCriteria={filterCriteria} />,
    },
    {
      label: 'Interview Personas',
      value: 'interview-personas',
      content: () => <InterviewPersonas />,
    },
    {
      label: 'Total Applicants',
      value: 'total-applicants',
      content: () => <JobApplicantsTable />,
    },
  ];

  return (
    <div>
      <DataTabs items={tabConfig} contentHeight='calc(100vh - 420px)' />
    </div>
  );
};
