import { RobotIcon } from '@/assets/icons';
import { Loader } from '@/components/shared/loader';
import { useGetJobPersonasQuery } from '@/hooks/api/use-persona';
import { useParams } from '@tanstack/react-router';
import { useMemo } from 'react';

export const InterviewPersonas = () => {
  const { jobId } = useParams({ from: '/_authenticated/admin/jobs/$jobId/' });
  const { data: personas, isLoading } = useGetJobPersonasQuery(jobId || '');

  const calculateTotalDuration = useMemo(
    () => (personas: AttachJobPersona[]) => {
      const totalMinutes = personas.reduce((sum, persona) => sum + persona.time_duration, 0);
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      if (hours > 0 && minutes > 0) return `${hours}h ${minutes}m`;
      if (hours > 0) return `${hours}h`;
      return `${minutes}m`;
    },
    []
  );

  if (isLoading) {
    return <Loader size='sm' className='flex h-[calc(100vh-355px)] items-center justify-center' />;
  }

  return (
    <div className=''>
      {personas && personas?.length > 0 ? (
        <div>
          <div className='border-b-strock mb-6 grid grid-cols-4 border-b px-8 py-6'>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Total Interview Persona</p>
              <p className='text-base font-medium text-black'>{personas.length}</p>
            </div>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Total Interview Persona</p>
              <p className='text-base font-medium text-black'>{calculateTotalDuration(personas)}</p>
            </div>
          </div>

          <div className='grid max-w-2xl gap-6 px-6'>
            {personas.map((persona) => (
              <div
                className='border-strock flex items-center gap-2 rounded-2xl border bg-white p-6'
                key={persona.id}
              >
                <div className='bg-custom-white mr-2 rounded-md p-3'>
                  <RobotIcon className='text-gray-dark' />
                </div>

                <div className='flex-1'>
                  <h3 className='mb-1 text-lg font-semibold text-black'>{persona.persona_title}</h3>
                  <div className='text-gray-light flex items-center text-sm'>
                    Interview Duration:
                    <span className='text-gray-dark ml-1 font-medium'>
                      {persona.time_duration} minutes
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className='flex h-[calc(100vh-355px)] w-full flex-col items-center justify-center'>
          <div className='bg-primary-50 rounded-2xl p-4'>
            <RobotIcon className='text-primary' />
          </div>
          <p className='text-gray-light mt-3 font-medium italic'>
            No persona attached to this job yet
          </p>
        </div>
      )}
    </div>
  );
};
