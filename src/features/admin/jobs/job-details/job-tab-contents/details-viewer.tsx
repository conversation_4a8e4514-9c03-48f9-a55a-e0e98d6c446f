import { ScrollArea } from '@/components/ui';

interface Props {
  description: string;
  filterCriteria: string;
}

export const DetailsViewer = ({ description, filterCriteria }: Props) => {
  return (
    <div className='relative h-full overflow-y-auto p-4'>
      <div className='ql-editor text-gray-dark grid grid-cols-3 gap-8'>
        <ScrollArea className='col-span-2'>
          <div
            dangerouslySetInnerHTML={{
              __html: description ?? '',
            }}
            style={{ overflow: 'hidden', gridColumn: 'span 2' }}
          />
        </ScrollArea>

        <div className='bg-custom-white sticky top-0 col-span-1 h-fit rounded-2xl p-6'>
          <h3 className='text-lg font-semibold'>Candidate Eligibility Settings</h3>
          {filterCriteria ? (
            <p className='text-gray-dark leading-[24px]'>{filterCriteria}</p>
          ) : (
            <p className='text-gray-light italic'>Not Added Yet</p>
          )}
        </div>
      </div>
    </div>
  );
};
