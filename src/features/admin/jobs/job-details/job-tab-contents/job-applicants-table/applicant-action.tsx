import { SimpleDropdown } from '@/components/ui';
import { useResendInvitationMutation } from '@/hooks/api/use-interview';
import { useParams } from '@tanstack/react-router';
import { Forward, Trash2 } from 'lucide-react';

interface Props {
  candidate: JobCandidate;
}

export const ApplicantAction = ({ candidate }: Props) => {
  const { jobId } = useParams({ from: '/_authenticated/admin/jobs/$jobId/' });

  const { mutate: resendInvitation, isPending } = useResendInvitationMutation();

  const handleResendInvitation = () => {
    if (!jobId) return;

    resendInvitation({ candidate_email: candidate.email, job_id: jobId! });
  };

  return (
    <SimpleDropdown
      items={[
        {
          label: 'Resend Link',
          onClick: handleResendInvitation,
          icon: <Forward />,
          disabled: isPending,
        },
        { label: 'Remove', onClick: () => {}, icon: <Trash2 />, disabled: true },
      ]}
    />
  );
};
