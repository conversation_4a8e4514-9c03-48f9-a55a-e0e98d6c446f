import { ApplicantAction } from './applicant-action';
import { Badge } from '@/components/ui';
import { JOB_CANDIDATE_STATUS } from '@/utils/constants';
import { snakeToTitleCase } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';

export const applicantColumns: ColumnDef<JobCandidate>[] = [
  {
    accessorKey: 'full_name',
    header: 'Applicant Name',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const getBadgeVariant = (status: string) => {
        switch (status) {
          case JOB_CANDIDATE_STATUS.COMPLETED:
            return 'success';
          case JOB_CANDIDATE_STATUS.IN_PROGRESS:
            return 'info';
          case JOB_CANDIDATE_STATUS.INCOMPLETE:
            return 'destructive';
          case JOB_CANDIDATE_STATUS.PENDING:
            return 'info';
          default:
            return 'secondary';
        }
      };

      return (
        <Badge key={row.original.id} variant={getBadgeVariant(row.original.interview_status)}>
          {snakeToTitleCase(row.original.interview_status)}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'is_eligible_for_interview',
    header: 'Eligible',
    cell: ({ row }) => {
      return row.original.is_eligible_for_interview
        ? row.original.is_eligible_for_interview
        : 'N/A';
    },
  },
  {
    accessorKey: 'interview_date',
    header: 'Interview Date',
    cell: ({ row }) => {
      return row.original.interview_date
        ? new Date(row.original.interview_date).toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
          })
        : '-';
    },
  },
  {
    accessorKey: 'actions',
    header: 'Actions',
    meta: {
      align: 'right',
    },
    cell: ({ row }) => {
      return <ApplicantAction candidate={row.original} />;
    },
  },
];
