import { applicantColumns } from './columns';
import {
  DataTable,
  DataTableContainer,
  DataTableFooter,
  DataTableHeader,
} from '@/components/data-table';
import { SearchInput } from '@/components/shared/search-input';
import TablePagination from '@/components/shared/table-pagination';
import { ScrollArea } from '@/components/ui';
import { useJobCandidatesQuery } from '@/hooks/api/use-job';
import { useTableState } from '@/hooks/use-table-state';
import { useNavigate, useParams } from '@tanstack/react-router';
import { useMemo } from 'react';

export const JobApplicantsTable = () => {
  const navigate = useNavigate();

  const { jobId } = useParams({ from: '/_authenticated/admin/jobs/$jobId/' });

  const { search, setSearch, debouncedSearch, pagination } = useTableState({
    initialFilters: {
      status: 'all',
    },
  });

  const { data: candidates, isFetching } = useJobCandidatesQuery({
    jobId: jobId || '',
    params: {
      search: debouncedSearch,
      limit: pagination.pageSize,
      offset: pagination.offset,
    },
  });

  const columns = useMemo(() => applicantColumns, []);

  return (
    <ScrollArea className='h-[calc(100vh-420px)]'>
      <DataTableContainer className='border-none'>
        <DataTableHeader>
          <DataTableHeader.Left>
            <SearchInput value={search} onChange={setSearch} />
          </DataTableHeader.Left>
        </DataTableHeader>
        <DataTable
          data={candidates?.items ?? []}
          columns={columns}
          isLoading={isFetching}
          onRowClick={(row) => navigate({ to: `/admin/applicants/${row.id}` })}
        />
      </DataTableContainer>
      <DataTableFooter>
        {!isFetching && <TablePagination totalRecords={parseInt(candidates?.total || '0', 10)} />}
      </DataTableFooter>
    </ScrollArea>
  );
};
