import HookFormItem from '@/components/hook-form/HookFormItem';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Form,
} from '@/components/ui';
import { getDateToISOString } from '@/utils/helper';
import { zodResolver } from '@hookform/resolvers/zod';
import { CalendarCheck } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const today = new Date();
today.setHours(0, 0, 0, 0);

const markAsActiveSchema = z
  .object({
    application_start_date: z
      .date({
        required_error: 'Start date is required',
        invalid_type_error: 'Invalid date',
      })
      .min(today, 'Start date must be today or in the future'),
    application_end_date: z
      .date({
        required_error: 'End date is required',
        invalid_type_error: 'Invalid date',
      })
      .min(today, 'End date must be in the future'),
  })
  .refine(
    (data) => {
      if (data.application_start_date && data.application_end_date) {
        return data.application_end_date > data.application_start_date;
      }
      return true;
    },
    {
      message: 'End date must be after start date',
      path: ['application_end_date'],
    }
  );

type MarkAsActiveFormData = z.infer<typeof markAsActiveSchema>;

interface MarkAsActiveModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  jobTitle: string;
  onConfirm: (data: { application_start_date: string; application_end_date: string }) => void;
  isLoading?: boolean;
}

export const MarkAsActiveModal: React.FC<MarkAsActiveModalProps> = ({
  open,
  onOpenChange,
  jobTitle,
  onConfirm,
  isLoading = false,
}) => {
  const form = useForm<MarkAsActiveFormData>({
    resolver: zodResolver(markAsActiveSchema),
  });

  const handleSubmit = (data: MarkAsActiveFormData) => {
    const formattedData = {
      application_start_date: getDateToISOString(data.application_start_date)!,
      application_end_date: getDateToISOString(data.application_end_date)!,
    };
    onConfirm(formattedData);
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <div className='mx-auto mb-4 flex size-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20'>
            <CalendarCheck className='size-6 text-green-600 dark:text-green-400' />
          </div>
          <DialogTitle className='text-center'>Publish Job</DialogTitle>
          <DialogDescription className='text-center'>
            Set the application period for <strong>{jobTitle}</strong>. This will make the job post
            publicly visible.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-4'>
            <HookFormItem
              name='application_start_date'
              label='Application Start Date'
              className='flex flex-col'
              labelClassName='!text-foreground font-semibold tracking-wide'
              isRequired
            >
              <DatePicker
                id='start-date'
                defaultDate={form.watch('application_start_date')}
                onChange={(date) => {
                  if (date) form.setValue('application_start_date', date);
                }}
              />
            </HookFormItem>

            <HookFormItem
              name='application_end_date'
              label='Application End Date'
              className='flex flex-col'
              labelClassName='!text-foreground font-semibold tracking-wide'
              isRequired
            >
              <DatePicker
                id='end-date'
                defaultDate={form.watch('application_end_date')}
                onChange={(date) => {
                  if (date) form.setValue('application_end_date', date);
                }}
              />
            </HookFormItem>

            <div className='flex gap-3 pt-4'>
              <Button type='button' variant='outline' onClick={handleClose} className='flex-1'>
                Cancel
              </Button>
              <Button
                type='submit'
                disabled={isLoading}
                className='flex-1 bg-green-600 hover:bg-green-700'
              >
                {isLoading ? 'Activating...' : 'Mark as Active'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
