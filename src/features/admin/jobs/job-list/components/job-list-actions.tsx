import { useJobStore } from '../../create-job/hooks/use-job-store';
import { INACTIVE, ACTIVE } from '../utils/constant';
import { MarkAsActiveModal } from './mark-as-active-modal';
import { ConfirmationModalInput } from '@/components/shared/confirmation-modal-input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useUpdateJobMutation, useDeleteJobMutation } from '@/hooks/api/use-job';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { useNavigate } from '@tanstack/react-router';
import { Ellipsis, Edit, Trash2, X, CheckCircle } from 'lucide-react';
import React, { useState } from 'react';

interface JobListActionsProps {
  job: JobList;
}

export const JobListActions: React.FC<JobListActionsProps> = ({ job }) => {
  const navigate = useNavigate();
  const { setDraftJobId } = useJobCreationStore();
  const { setCurrentStep } = useJobStore();
  const { mutate: updateJobStatus, isPending: isUpdating } = useUpdateJobMutation();
  const { mutateAsync: deleteJob } = useDeleteJobMutation();
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isMarkInactiveConfirmModalOpen, setIsMarkInactiveConfirmModalOpen] = useState(false);
  const [isMarkActiveModalOpen, setIsMarkActiveModalOpen] = useState(false);

  const handleEdit = () => {
    setDraftJobId(job.id);
    setCurrentStep(1);
    navigate({ to: '/admin/jobs/edit' });
  };

  const handleMarkInactive = async () => {
    await updateJobStatus({
      id: job.id,
      payload: { status: INACTIVE },
    });
    setIsMarkInactiveConfirmModalOpen(false);
  };

  const handleMarkInactiveClick = () => {
    setIsMarkInactiveConfirmModalOpen(true);
  };

  const handleMarkActive = async (data: {
    application_start_date: string;
    application_end_date: string;
  }) => {
    updateJobStatus({
      id: job.id,
      payload: {
        status: ACTIVE,
        application_start_date: data.application_start_date,
        application_end_date: data.application_end_date,
      },
    });
    setIsMarkActiveModalOpen(false);
  };

  const handleMarkActiveClick = () => {
    setIsMarkActiveModalOpen(true);
  };

  const handleDeleteJob = async () => {
    await deleteJob(job.id);
    setIsConfirmModalOpen(false);
  };

  const handleRemoveClick = () => {
    setIsConfirmModalOpen(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className='rounded-sm p-1 hover:bg-neutral-200'>
            <Ellipsis className='size-4 cursor-pointer' />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='font-manrope min-w-[120px]'>
          <DropdownMenuItem
            onClick={handleEdit}
            disabled={job.status === ACTIVE || job.status === INACTIVE}
          >
            <Edit className='mr-2 size-4' /> Edit job post
          </DropdownMenuItem>
          {job.status === ACTIVE ? (
            <DropdownMenuItem onClick={handleMarkInactiveClick}>
              <X className='mr-2 size-4' /> Mark as Inactive
            </DropdownMenuItem>
          ) : job.status === INACTIVE ? (
            <DropdownMenuItem onClick={handleMarkActiveClick}>
              <CheckCircle className='mr-2 size-4' /> Mark as Active
            </DropdownMenuItem>
          ) : null}
          <DropdownMenuItem
            onClick={handleRemoveClick}
            className='text-destructive hover:bg-destructive/10 focus:bg-destructive/10'
          >
            <Trash2 className='text-destructive mr-2 size-4' /> Remove
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <ConfirmationModalInput
        title='Mark Job as Inactive'
        description={`Are you sure you want to mark ${job.title} job post as inactive? This will remove the job post from being publicly visible.`}
        onConfirm={handleMarkInactive}
        open={isMarkInactiveConfirmModalOpen}
        onOpenChange={setIsMarkInactiveConfirmModalOpen}
        confirmationText='INACTIVE'
        confirmationPlaceholder='Enter INACTIVE to confirm'
        iconClassName='size-6 text-blue-600 dark:text-blue-400'
        iconWrapperClassName='bg-blue-100 dark:bg-blue-900/20'
        confirmButtonClassName='bg-blue-700'
      />

      <ConfirmationModalInput
        title={`Remove Job`}
        description={`Are you sure you want to remove ${job.title} job post? All data related to this job post will be permanently deleted.`}
        onConfirm={handleDeleteJob}
        open={isConfirmModalOpen}
        onOpenChange={setIsConfirmModalOpen}
        confirmationText='DELETE'
        confirmationPlaceholder='Enter DELETE to confirm deletion'
        showWarning
      />

      <MarkAsActiveModal
        open={isMarkActiveModalOpen}
        onOpenChange={setIsMarkActiveModalOpen}
        jobTitle={job.title}
        onConfirm={handleMarkActive}
        isLoading={isUpdating}
      />
    </>
  );
};
