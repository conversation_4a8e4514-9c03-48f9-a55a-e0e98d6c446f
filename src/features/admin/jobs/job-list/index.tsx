import { useJobStore } from '../create-job/hooks/use-job-store';
import { StatusFilter } from './components/status-filter';
import { jobColumns } from './utils/columns';
import {
  DataTable,
  DataTableContainer,
  DataTableFooter,
  DataTableHeader,
} from '@/components/data-table';
import { PageHeader } from '@/components/shared/PageHeader';
import { SearchInput } from '@/components/shared/search-input';
import TablePagination from '@/components/shared/table-pagination';
import { Button } from '@/components/ui';
import { useJobsQuery } from '@/hooks/api/use-job';
import { useDebounce } from '@/hooks/use-debounce';
import { usePagination } from '@/hooks/use-pagination';
import { useJobCreationStore } from '@/stores/job-creation-store';
import { useNavigate } from '@tanstack/react-router';
import { Plus } from 'lucide-react';
import { useMemo, useState } from 'react';

export default function JobList() {
  const navigate = useNavigate();
  const [status, setStatus] = useState<string | undefined>(undefined);
  const [search, setSearch] = useState('');
  const debouncedSearch = useDebounce(search, 400);
  const { clearDraftJobId } = useJobCreationStore();
  const { setCurrentStep } = useJobStore();

  const { currentPageNumber, currentPageSize } = usePagination({});

  const offset = (currentPageNumber - 1) * currentPageSize;

  const { data: jobs, isLoading } = useJobsQuery(
    { limit: currentPageSize, offset, status, search: debouncedSearch },
    { enabled: true }
  );

  const columns = useMemo(() => jobColumns, []);

  const handleNewJobPost = () => {
    clearDraftJobId();
    setCurrentStep(1);
    navigate({ from: '/admin/jobs', to: '/admin/jobs/create' });
  };

  return (
    <div className='bg-custom-white flex min-h-screen flex-col'>
      <PageHeader title='Job List' />

      <div className='flex-1 overflow-auto p-6 pt-0'>
        <DataTableContainer>
          <DataTableHeader>
            <DataTableHeader.Left>
              <div className='flex items-center gap-3'>
                <SearchInput value={search} onChange={setSearch} />
                <StatusFilter value={status} onChange={setStatus} />
              </div>
            </DataTableHeader.Left>
            <DataTableHeader.Right>
              <Button onClick={handleNewJobPost}>
                <Plus /> Add new job post
              </Button>
            </DataTableHeader.Right>
          </DataTableHeader>
          <DataTable
            data={jobs?.items || []}
            columns={columns}
            isLoading={isLoading}
            onRowClick={(row) =>
              navigate({ from: '/admin/jobs', to: '/admin/jobs/$jobId', params: { jobId: row.id } })
            }
          />
          <DataTableFooter>
            <TablePagination totalRecords={jobs?.total || 0} />
          </DataTableFooter>
        </DataTableContainer>
      </div>
    </div>
  );
}
