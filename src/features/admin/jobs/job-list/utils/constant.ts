export const JOB_STATUS = {
  ACTIVE: 'active',
  SCHEDULED: 'scheduled',
  DRAFT: 'draft',
  INACTIVE: 'inactive',
  EXPIRED: 'expired',
} as const;

export const { ACTIVE, SCHEDULED, DRAFT, INACTIVE, EXPIRED } = JOB_STATUS;

// Type for job status
export type JobStatus = (typeof JOB_STATUS)[keyof typeof JOB_STATUS];

// Status color mappings
export const callTypes = new Map<string, string>([
  [ACTIVE, 'bg-teal-100/90 text-teal-900 dark:text-teal-900 border-teal-300'],
  [INACTIVE, 'bg-neutral-200/90 text-neutral-600 dark:text-neutral-900 border-neutral-400'],
  [SCHEDULED, 'bg-sky-200/40 text-sky-900 dark:text-sky-100 border-sky-300'],
  [DRAFT, 'bg-amber-50 text-orange-800 dark:text-orange-100 border-amber-300'],
  [
    EXPIRED,
    'bg-destructive/30 dark:bg-destructive/50 text-destructive dark:text-primary border-destructive/10',
  ],
]);
