import { JobListActions } from '../components/job-list-actions';
import { ACTIVE, INACTIVE } from './constant';
import { Badge } from '@/components/ui';
import { getStatusBadgeVariant, snakeToTitleCase } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';
import { format, formatDistanceStrict, endOfDay, isBefore } from 'date-fns';

export const jobColumns: ColumnDef<JobList>[] = [
  {
    accessorKey: 'title',
    header: 'Job Title',
  },
  {
    accessorKey: 'location',
    header: 'Location',
    cell: ({ row }) => row.original.location || '-',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <Badge variant={getStatusBadgeVariant(row.original.status)}>
        {snakeToTitleCase(row.original.status)}
      </Badge>
    ),
  },
  {
    accessorKey: 'experience',
    header: 'Experience (years)',
    meta: {
      align: 'center',
    },
    cell: ({ row }) => {
      const { min_exp, max_exp } = row.original;
      if (min_exp || max_exp) return `${min_exp ?? '-'} - ${max_exp ?? '-'}`;
      return '-';
    },
  },
  {
    accessorKey: 'deadline',
    header: 'Deadline',
    cell: ({ row }) => {
      const { application_end_date, status } = row.original;
      if (!application_end_date) return '-';

      const deadline = endOfDay(new Date(application_end_date));
      const today = new Date();

      const formattedDate = format(deadline, 'd MMM yyyy');
      const showTimeBadge = (status === ACTIVE || status === INACTIVE) && application_end_date;

      return (
        <div className='flex items-center gap-2'>
          <div>{formattedDate}</div>
          {showTimeBadge && (
            <Badge
              variant={isBefore(today, deadline) ? 'secondary' : 'destructive'}
              className='text-xs'
            >
              {isBefore(today, deadline)
                ? `${formatDistanceStrict(deadline, today)} left`
                : 'Expired'}
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'totalCandidates',
    header: 'Total Candidates',
    cell: ({ row }) => (
      <div className='flex items-center justify-center text-center'>
        {row.original.candidate_count?.total ?? 0}
      </div>
    ),
  },
  {
    accessorKey: 'eligibleCandidates',
    header: 'Eligible Candidates',
    cell: ({ row }) => (
      <div className='flex items-center justify-center text-center'>
        {row.original.candidate_count?.eligible ?? '-'}
      </div>
    ),
  },
  {
    accessorKey: 'attendedCandidates',
    header: 'Attended Candidates',
    cell: ({ row }) => (
      <div className='flex items-center justify-center text-center'>
        {row.original.candidate_count?.attended ?? 0}
      </div>
    ),
  },
  {
    accessorKey: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <div
        className='flex items-center justify-center text-center'
        onClick={(e) => e.stopPropagation()}
      >
        <JobListActions job={row.original} />
      </div>
    ),
  },
];
