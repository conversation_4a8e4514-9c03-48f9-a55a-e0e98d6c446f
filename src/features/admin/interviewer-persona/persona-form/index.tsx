import { FormTabContents } from './form-tab-contents';
import { PageHeader } from '@/components/shared/PageHeader';
import { AnimatePresence, motion } from 'framer-motion';

interface Props {
  action: 'create' | 'edit';
}

const PersonaForm = ({ action: mode }: Props) => {
  const breadcrumbsItem = [
    { label: 'Home', href: '/admin/dashboard' },
    { label: 'Persona List', href: '/admin/interviewer-persona' },
    { label: mode === 'create' ? 'Create a interview persona' : 'Edit a interview persona' },
  ];

  return (
    <div className='dark:bg-background bg-custom-white flex min-h-screen flex-col'>
      <PageHeader
        title={' Interview Persona'}
        showNavigation={true}
        breadcrumbs={breadcrumbsItem}
      />

      <div className='flex flex-1 overflow-y-auto px-6'>
        <div className='bg-card mx-auto mb-4 min-h-max w-full rounded-2xl border'>
          {/* Header */}
          <div className='border-b-strock flex items-center justify-between gap-4 rounded-t-2xl border-b px-6 py-8'>
            <div className='flex items-center gap-3'>
              <h1 className='text-2xl font-bold'>
                {mode === 'create' ? 'Create New Persona' : 'Edit Persona'}
              </h1>
            </div>
          </div>

          {/* Content */}
          <div className='relative isolate overflow-hidden p-10'>
            <AnimatePresence mode='wait'>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className='z-30 mx-auto flex max-w-2xl flex-col items-center'
              >
                <h2 className='mb-1.5 text-xl font-semibold'>Set Up Your AI Interview Persona</h2>
                <p className='text-gray-light text-center leading-[140%]'>
                  Choose your preferred method to create an interview persona. Use the guided form
                  to auto-generate a smart prompt based on inputs, or switch to manual mode to write
                  your own custom prompt from scratch.
                </p>

                <FormTabContents />
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
};
export default PersonaForm;
