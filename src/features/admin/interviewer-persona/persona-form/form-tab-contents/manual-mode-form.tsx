import { useGetPersona } from '../hooks/use-get-persona';
import { usePersonaFormAction } from '../hooks/use-persona-form-action';
import { usePersonaStore } from '../hooks/use-persona-store';
import HookFormItem from '@/components/hook-form/HookFormItem';
import { Button, Form, Input } from '@/components/ui';
import { AutosizeTextarea } from '@/components/ui/autosize-textarea';
import { manualModePersonaSchema, ManualModePersonaType } from '@/validations/personaSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

const ManualModeForm = () => {
  const form = useForm<ManualModePersonaType>({
    resolver: zodResolver(manualModePersonaSchema),
    defaultValues: {
      title: '',
      description: '',
      prompt_text: '',
    },
  });

  const { createPersona, isCreating, updatePersona, isUpdating } = usePersonaFormAction(form, {
    mode: 'manual',
  });

  const { action } = useGetPersona();
  const { personaFormData } = usePersonaStore();

  useEffect(() => {
    if (personaFormData) {
      form.reset(personaFormData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [personaFormData]);

  const onSubmit = (data: ManualModePersonaType) => {
    if (action === 'edit') {
      const changedFields = Object.keys(form.formState.dirtyFields);

      const payload = changedFields.reduce((acc, field) => {
        acc[field as keyof ManualModePersonaType] = data[field as keyof ManualModePersonaType];
        return acc;
      }, {} as Partial<ManualModePersonaType>);

      updatePersona(personaFormData.id, payload);
    } else {
      createPersona({ ...data, prompt_generation_metadata: {} });
    }
  };

  return (
    <section className='p-10'>
      <Form {...form}>
        {' '}
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          <HookFormItem name='title' label='Persona Name'>
            <Input placeholder='Interview for Javascript developer' />
          </HookFormItem>

          <HookFormItem name='description' label='Description'>
            <AutosizeTextarea
              placeholder='Write your persona description here...'
              className='resize-none'
            />
          </HookFormItem>

          <HookFormItem name='prompt_text' label='Custom Prompt Instruction'>
            <AutosizeTextarea
              placeholder='Write your interview instruction here...'
              className='resize-none'
            />
          </HookFormItem>

          <Button
            className='relative w-full overflow-hidden font-semibold'
            loading={isCreating || isUpdating}
          >
            <span className='relative z-10'>
              {action === 'edit' ? 'Update' : 'Create'} & Preview Interview
            </span>
          </Button>
        </form>
      </Form>
    </section>
  );
};
export default ManualModeForm;
