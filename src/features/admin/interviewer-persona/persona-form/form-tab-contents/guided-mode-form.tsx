import { useGetPersona } from '../hooks/use-get-persona';
import { usePersonaFormAction } from '../hooks/use-persona-form-action';
import { usePersonaStore } from '../hooks/use-persona-store';
import HookFormItem from '@/components/hook-form/HookFormItem';
import SimpleSelect from '@/components/select/simple-selector';
import { Button, Form, Input, Skeleton } from '@/components/ui';
import { AutosizeTextarea } from '@/components/ui/autosize-textarea';
import { TagsInput } from '@/components/ui/tags-input';
import { useGeneratePromptMutation } from '@/hooks/api/use-persona';
import { difficulties } from '@/utils/data';
import { getPersonaSchema, GuidedModePersonaType } from '@/validations/personaSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Sparkles } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

export const GuidedModeForm = () => {
  const [showAiGeneration, setShowAiGeneration] = useState(false);

  const form = useForm<GuidedModePersonaType>({
    resolver: zodResolver(getPersonaSchema(showAiGeneration)),
    defaultValues: {
      title: '',
      role: '',
      difficulty: '',
      keywords: [],
      expectations: '',
      description: '',
      prompt_text: '',
    },
  });

  const { personaFormData } = usePersonaStore();

  const { action } = useGetPersona();
  const { createPersona, isCreating, updatePersona, isUpdating } = usePersonaFormAction(form, {
    mode: 'guided',
  });

  const { mutate: generatePrompt, isPending: isGeneratingPrompt } = useGeneratePromptMutation();

  useEffect(() => {
    if (personaFormData && action === 'edit') {
      setShowAiGeneration(true);
      form.reset(personaFormData);

      const metaData = personaFormData.prompt_generation_metadata;

      form.setValue('expectations', metaData.expectations as string);
      form.setValue('keywords', metaData.keywords as string[]);
      form.setValue('role', metaData.role as string);
      form.setValue('difficulty', metaData.difficulty as string);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [personaFormData]);

  const handlePromptGeneration = () => {
    const { role, difficulty, keywords, expectations } = form.getValues();

    const metaData = {
      role: role,
      difficulty,
      keywords,
      expectations,
    };

    generatePrompt(
      { ...metaData },
      {
        onSuccess: (response) => {
          form.setValue('prompt_text', response.prompt_text ?? '');
          form.setValue('description', response.description ?? '');
          setShowAiGeneration(true);
        },
      }
    );
  };

  const onSubmit = (data: GuidedModePersonaType) => {
    const { prompt_text, title, description, ...rest } = data;

    if (showAiGeneration) {
      const payload = {
        title: title,
        description: description!,
        prompt_text: prompt_text!,
        prompt_generation_metadata: { ...rest },
      };

      if (action === 'edit') {
        updatePersona(personaFormData.id, payload);
        return;
      } else {
        createPersona(payload);
      }
    } else handlePromptGeneration();
  };

  return (
    <section className='p-10'>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          <HookFormItem name='title' label='Persona Name'>
            <Input placeholder='Interview for Javascript developer' />
          </HookFormItem>

          <div className='grid grid-cols-2 items-baseline gap-4'>
            <HookFormItem name='role' label='Role/Position'>
              <Input placeholder='eg. Frontend Developer' />
            </HookFormItem>
            <HookFormItem name='difficulty' label='Difficulty'>
              <SimpleSelect options={difficulties} placeholder='Select role' />
            </HookFormItem>
          </div>

          <HookFormItem name='keywords' label='Keywords'>
            <TagsInput initialTags={form.watch('keywords')} />
          </HookFormItem>

          <HookFormItem name='expectations' label='What to look in candidates'>
            <AutosizeTextarea
              placeholder='Explain your expectations from candidates...'
              className='resize-none'
            />
          </HookFormItem>

          {isGeneratingPrompt && !showAiGeneration && (
            <div>
              <Skeleton className='mb-3.5 h-6 w-20' />
              <Skeleton className='h-40 w-full' />
            </div>
          )}

          {showAiGeneration && (
            <>
              <HookFormItem name='description' label='Description'>
                <AutosizeTextarea
                  placeholder='Write your persona description here...'
                  className='resize-none leading-6'
                />
              </HookFormItem>
              <HookFormItem name='prompt_text' label='AI Generated Prompt Instruction'>
                <AutosizeTextarea className='gradient-border focus-ring-none resize-none rounded-lg leading-6' />
              </HookFormItem>
            </>
          )}

          {showAiGeneration ? (
            <div className='grid grid-cols-2 gap-4'>
              <Button
                type='button'
                variant={'secondary'}
                disabled={isCreating}
                loading={isGeneratingPrompt}
                onClick={handlePromptGeneration}
              >
                Regenerate Prompt
              </Button>
              <Button
                className='relative w-full overflow-hidden font-semibold'
                disabled={isGeneratingPrompt}
                loading={isCreating || isUpdating}
              >
                <span className='relative z-10'>
                  {action === 'edit' ? 'Update' : 'Create'} & Preview Interview
                </span>
              </Button>
            </div>
          ) : (
            <Button
              className='bg-logo-gradient relative w-full overflow-hidden font-semibold'
              loading={isGeneratingPrompt}
            >
              <Sparkles />
              <span className='relative z-10'>Create Description with AI</span>
            </Button>
          )}
        </form>
      </Form>
    </section>
  );
};
