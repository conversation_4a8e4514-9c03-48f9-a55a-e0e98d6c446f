import { useGetPersona } from '../hooks/use-get-persona';
import { PersonaPreviewModal } from '../persona-preview-modal';
import { GuidedModeForm } from './guided-mode-form';
import ManualModeForm from './manual-mode-form';
import { DataTabs } from '@/components/data-tabs';
import { Loader } from '@/components/shared/loader';
import { PersonaCreationMode } from '@/stores/slices/persona-slice';

export const FormTabContents = () => {
  const { action, mode, isLoading } = useGetPersona();

  const tabContents: TabItem[] = [
    {
      label: 'Guided Mode',
      value: 'guided-mode',
      content: () => <GuidedModeForm />,
      disabled: action === 'edit' && mode === PersonaCreationMode.MANUAL,
    },
    {
      label: 'Manual Mode',
      value: 'manual-mode',
      content: () => <ManualModeForm />,
      disabled: action === 'edit' && mode === PersonaCreationMode.GUIDED,
    },
  ];

  if (isLoading) {
    return <Loader className='py-20' />;
  }

  return (
    <div className='mt-6 w-full'>
      <DataTabs
        items={tabContents}
        className='h-12 w-full'
        defaultTabIndex={action === 'edit' ? (mode === PersonaCreationMode.GUIDED ? 0 : 1) : 0}
      />

      <PersonaPreviewModal />
    </div>
  );
};
