import { usePersonaStore } from '../hooks/use-persona-store';
import { LiveInterviewPreview } from './live-interview-preview';
import { PreviewTabContents } from './preview-tab-contents';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui';
import { usePersonaPreviewModal } from '@/features/admin/interviewer-persona/persona-form/hooks/use-persona-preview-modal';
import { useUpdatePersonaMutation } from '@/hooks/api/use-persona';
import { useNavigate } from '@tanstack/react-router';
import { X } from 'lucide-react';

interface Props {
  skipAction?: boolean; // skipping action for preview mode
}

export const PersonaPreviewModal = ({ skipAction = false }: Props) => {
  const navigate = useNavigate();

  const { mutate: updatePersona, isPending: isUpdating } = useUpdatePersonaMutation();
  const { resetPersonaForm, personaFormData } = usePersonaStore();
  const { isOpen, closeModal } = usePersonaPreviewModal();

  const handleClose = () => {
    resetPersonaForm();
    navigate({ to: '/admin/interviewer-persona' });
    closeModal();
  };

  const handleSave = () => {
    updatePersona(
      {
        id: personaFormData.id!,
        persona: {
          description: personaFormData.description,
          prompt_text: personaFormData.prompt_text,
        },
      },
      {
        onSuccess: () => {
          handleClose();
        },
      }
    );
  };

  return (
    <Dialog open={isOpen}>
      <DialogContent className='overflow-hidden rounded-3xl p-0 sm:max-w-[93%]' hideCloseButton>
        <DialogHeader className='bbg-red-400 p-0'>
          <DialogTitle className='sr-only h-0'>Interview preview</DialogTitle>
          <DialogDescription className='sr-only'>
            This is how the interview will look to candidates.
          </DialogDescription>
        </DialogHeader>
        <section className='flex h-[calc(100vh-125px)] flex-col'>
          <header className='border-b-strock flex items-center justify-between border-b'>
            <div className='flex items-center gap-4 px-6 pt-2 pb-6'>
              <h2 className='text-2xl font-semibold'>{personaFormData.title ?? 'Mock Persona'}</h2>
              <span className='bg-gray-light h-4 w-0.5' />
              <span className='text-gray-dark text-lg'>Preview</span>
            </div>
            {skipAction ? (
              <div className='px-6 pt-2 pb-6'>
                <Button
                  size={'icon'}
                  onClick={() => {
                    resetPersonaForm();
                    closeModal();
                  }}
                  variant={'secondary'}
                >
                  <X />
                </Button>
              </div>
            ) : (
              <div className='flex gap-2 px-6 pt-2 pb-6'>
                <Button
                  type='button'
                  size={'lg'}
                  variant={'outline'}
                  onClick={handleClose}
                  disabled={isUpdating}
                  className='w-24'
                >
                  Cancel
                </Button>{' '}
                <Button size={'lg'} onClick={handleSave} loading={isUpdating} className='w-24'>
                  Save
                </Button>
              </div>
            )}
          </header>

          <main className='grid flex-1 grid-cols-2 gap-1'>
            <PreviewTabContents skipAction={skipAction} />
            <LiveInterviewPreview />
          </main>
        </section>
      </DialogContent>
    </Dialog>
  );
};
