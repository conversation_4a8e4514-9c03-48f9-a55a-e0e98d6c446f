'use client';

import { usePersonaStore } from '../hooks/use-persona-store';
import { RobotIcon } from '@/assets/icons';
import { TypingIndicator } from '@/components/shared/typing-indicator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { generateUniqueId } from '@/features/admin/jobs/create-job/utils/helper';
import {
  useGenerateQuestionSyncMutation,
  useUpdateAnswerMutation,
} from '@/hooks/api/use-interview';
import { usePrepareLiveInterviewMutation } from '@/hooks/api/use-prompt';
import { HelpCircle, Send, User } from 'lucide-react';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
}

export function LiveInterviewPreview() {
  const { mutate: generateQuestion, isPending } = useGenerateQuestionSyncMutation();
  const { mutate: updateAnswer } = useUpdateAnswerMutation();
  const { mutate: prepareLiveInterview, isPending: isPreparingLiveInterview } =
    usePrepareLiveInterviewMutation();

  const { configuration, personaFormData, cvInfo } = usePersonaStore();

  const [messages, setMessages] = useState<Message[]>([]);

  const [inputValue, setInputValue] = useState('');
  const [isAiTyping, setIsAiTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const channelIdRef = useRef<string | null>(null);

  if (!channelIdRef.current) {
    channelIdRef.current = generateUniqueId();
  }
  const channelId = channelIdRef.current;

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isAiTyping]);

  useEffect(() => {
    return () => {
      channelIdRef.current = null;
    };
  }, []);

  const generateNewQuestion = () => {
    setIsAiTyping(true);
    generateQuestion(
      {
        channel_id: channelId,
        llm_service_name: configuration.llm,
      },
      {
        onSuccess: (data) => {
          setIsAiTyping(false);
          setMessages((prev) => [
            ...prev,
            {
              id: data.chat_id,
              content: data.content,
              sender: 'ai',
            },
          ]);
        },
      }
    );
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage: Message = {
      id: generateUniqueId(),
      content: inputValue,
      sender: 'user',
    };

    setMessages((prev) => [...prev, newMessage]);

    setInputValue('');

    updateAnswer(
      {
        channel_id: channelId,
        chat_id: messages[messages.length - 1].id,
        answer: inputValue,
      },
      {
        onSuccess: () => {
          generateNewQuestion();
        },
      }
    );
  };

  const handleStartInterview = () => {
    prepareLiveInterview(
      {
        channel_id: channelId,
        prompt_text: personaFormData.prompt_text,
        cv_link: cvInfo.url,
        tts_language: 'en',
        apply_tts: false,
      },
      {
        onSuccess: () => {
          generateNewQuestion();
        },
      }
    );
  };

  const handleEndInterview = () => {
    setMessages([]);
    channelIdRef.current = generateUniqueId();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!messages.length) {
    return (
      <div className='bg-custom-white relative flex h-full items-center justify-center'>
        {!cvInfo.id && (
          <div className='text-gray-dark absolute inset-x-4 top-4 flex items-center gap-3 rounded-lg bg-[#FFF3E0] p-4 text-sm leading-5 font-medium'>
            <HelpCircle className='size-6 shrink-0 text-[#F97316]' />
            <p>
              Please upload a CV or select a dummy CV. Our AI will analyze the CV along with the
              interview instructions to generate personalized, relevant questions for the candidate.
            </p>
          </div>
        )}
        <Button
          className='w-60'
          onClick={handleStartInterview}
          disabled={!cvInfo.id}
          loading={isPending || isPreparingLiveInterview}
          loadingText={isPreparingLiveInterview ? 'Preparing...' : 'Generating question...'}
        >
          Start Interview
        </Button>
      </div>
    );
  }

  return (
    <div className='bg-custom-white border-l-strock flex h-[calc(100vh-190px)] flex-col border-l'>
      {/* Header */}
      <div className='bg-card border-strock border-b-strock mb-4 flex items-center justify-between border-b p-4'>
        <h1 className='text-foreground text-xl font-semibold'>AI Interview Agent</h1>
        <Button
          variant='default'
          className='bg-logo-gradient'
          size={'lg'}
          onClick={handleEndInterview}
        >
          Finish Interview
        </Button>
      </div>

      {/* Messages Container */}
      <div className='relative flex-1 overflow-y-auto px-4'>
        {messages.map((message) => (
          <div
            key={message.id}
            className={`mb-4 flex items-start gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.sender === 'ai' && (
              <Avatar className='bg-primary-100 h-10 w-10 flex-shrink-0'>
                <AvatarFallback className='bg-primary-100'>
                  <RobotIcon className='text-primary-600 h-5 w-5' />
                </AvatarFallback>
              </Avatar>
            )}

            <div
              className={`max-w-[70%] rounded-2xl px-4 py-3 ${
                message.sender === 'user'
                  ? 'from-primary-500 to-primary-600 ml-auto bg-gradient-to-b text-white'
                  : 'bg-card text-card-foreground border'
              }`}
            >
              <p className='text-sm leading-relaxed'>{message.content}</p>
            </div>

            {message.sender === 'user' && (
              <Avatar className='h-10 w-10 flex-shrink-0 bg-white'>
                <AvatarFallback className='bg-white'>
                  <User className='h-5 w-5 text-gray-600' />
                </AvatarFallback>
              </Avatar>
            )}
          </div>
        ))}

        {isAiTyping && (
          <div className='mb-4'>
            <TypingIndicator />
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className='border-t bg-transparent p-4'>
        <div className='mx-auto flex max-w-4xl items-center gap-2'>
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder='Write your answer here...'
            className='border-input flex-1 rounded-full px-4 py-2'
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim()}
            size='icon'
            className='bg-primary-500 hover:bg-primary-600 rounded-full text-white'
          >
            <Send className='h-4 w-4' />
          </Button>
        </div>
      </div>
    </div>
  );
}
