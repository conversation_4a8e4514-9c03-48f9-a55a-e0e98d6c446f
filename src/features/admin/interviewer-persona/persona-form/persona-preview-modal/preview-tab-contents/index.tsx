import { InterviewInformation } from '../../../persona-details/persona-tab-contents/interview-information';
import { usePersonaStore } from '../../hooks/use-persona-store';
import { DummyCvUploader } from './dummy-cv-uploader';
import { InformationEditor } from './information-editor';
import { PreviewConfig } from './preview-config';
import { DataTabs } from '@/components/data-tabs';

interface Props {
  skipAction?: boolean;
}

export const PreviewTabContents = ({ skipAction }: Props) => {
  const { personaFormData } = usePersonaStore();

  const tabContents: TabItem[] = [
    {
      label: 'Interview Information',
      value: 'interview-information',
      content: () =>
        skipAction ? (
          <InterviewInformation persona={personaFormData} className='h-full' />
        ) : (
          <InformationEditor persona={personaFormData} className='h-full' />
        ),
      className: skipAction ? '' : 'border-0 rounded-none px-1',
    },
    {
      label: 'Candidate CV',
      value: 'candidate-cv',
      content: () => <DummyCvUploader />,
      className: 'border-0 rounded-none',
    },
    {
      label: 'Configuration',
      value: 'configuration',
      content: () => <PreviewConfig />,
      className: 'border-0 rounded-none',
    },
  ];
  return (
    <div className='p-6'>
      <DataTabs items={tabContents} contentHeight='calc(100vh - 300px)' />
    </div>
  );
};
