import { usePersonaStore } from '../../hooks/use-persona-store';
import FileUploader from '@/components/file-uploader/FileUploader';
import SimpleSelect from '@/components/select/simple-selector';
import { Button, Label } from '@/components/ui';
import { ApplicantCV } from '@/features/admin/applicants/applicant-details/applicant-tab-contents/applicant-cv';
import {
  useGetPersonaPreviewCVsQuery,
  useGetPersonaPreviewCVUrlQuery,
  useUploadPersonaPreviewCVMutation,
} from '@/hooks/api/use-prompt';
import { motion } from 'framer-motion';
import { FileText } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export const DummyCvUploader = () => {
  const { personaFormData, cvInfo, setCvInfo } = usePersonaStore();

  const [cvUrl, setCVUrl] = useState('');
  const [cvId, setCvId] = useState('');

  const { mutate: uploadPersonaCV, isPending } = useUploadPersonaPreviewCVMutation();
  const { data: url, isLoading } = useGetPersonaPreviewCVUrlQuery({
    personaId: personaFormData.id || '',
    cvId: cvId || cvInfo.id,
  });

  const { data: cvs } = useGetPersonaPreviewCVsQuery(personaFormData.id || '', {
    enabled: !(cvId || cvInfo.id),
  });

  useEffect(() => {
    if (url) {
      setCVUrl(url.presigned_url);
      setCvInfo({
        url: url.presigned_url,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url]);

  const formatCvOptions = (cvs: { id: string; filename: string }[]) => {
    if (!cvs || cvs.length === 0) return [];

    return cvs.map((cv) => ({
      label: cv.filename,
      value: cv.id,
    }));
  };

  const handleUploadCV = async (files: File[]) => {
    if (files.length === 0) {
      toast.error('Please select a file to upload');
      return;
    }

    if (!personaFormData.id) {
      toast.error('No persona selected to upload CV');
      return;
    }

    uploadPersonaCV(
      { personaId: personaFormData.id, files },
      {
        onSuccess: (response) => {
          setCvId(response.id);
          setCvInfo({
            id: response.id,
            filename: response.filename,
          });
          toast.success('CV uploaded successfully');
        },

        onError: () => {},
      }
    );
  };

  const handleCVEdit = () => {
    setCvId('');
    setCvInfo({
      id: '',
      filename: '',
    });
    setCVUrl('');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className='p-1'
    >
      {cvUrl ? (
        <div className=''>
          <div className='border-strock mb-4 flex items-center justify-between overflow-hidden rounded-lg border p-4'>
            <div>
              <Label>Selected CV</Label>
              <div className='text-gray-dark mt-1.5 flex items-center gap-2'>
                <FileText className='size-4' />
                {cvInfo.filename}
              </div>
            </div>
            <Button variant={'secondary'} size={'lg'} onClick={handleCVEdit}>
              Edit
            </Button>
          </div>
          <ApplicantCV
            cvLink={cvUrl}
            pdfLink={{ url: cvUrl }}
            isLoading={isPending || isLoading}
            isError={false}
          />
        </div>
      ) : (
        <div className='py-4'>
          <div>
            <Label className='mb-3.5'>Select a dummy CV</Label>
            <SimpleSelect
              options={formatCvOptions(cvs ?? [])}
              value={cvInfo.id}
              onChange={(val) =>
                setCvInfo({
                  id: val.toString(),
                  filename: cvs?.find((cv) => cv.id === val)?.filename || '',
                })
              }
            />
          </div>

          <div className='my-8 flex items-center gap-4'>
            <span className='bg-strock h-0.25 flex-1' />
            <span className='text-gray-light'>OR</span>
            <span className='bg-strock h-0.25 flex-1' />
          </div>

          <div className='relative'>
            <FileUploader
              placeholder='Drag your file or'
              placeHolder2='browse'
              supportedFormats='Max 2 MB files are allowed'
              acceptedFileTypes={{ 'application/pdf': ['.pdf'], 'application/msword': ['.doc'] }}
              onFilesUploaded={(files) => handleUploadCV(files)}
              isLoading={isPending}
              maxFileSize={2 * 1024 * 1024} // 2 MB
              clearFiles={!isPending && !cvUrl}
            />

            <p className='text-gray-light mt-3 text-sm'>Only supports PDF and DOC files</p>
          </div>
        </div>
      )}
    </motion.div>
  );
};
