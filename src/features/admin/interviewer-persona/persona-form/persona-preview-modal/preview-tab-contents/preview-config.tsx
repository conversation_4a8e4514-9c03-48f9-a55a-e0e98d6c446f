import { usePersonaStore } from '../../hooks/use-persona-store';
import { Badge, ScrollArea } from '@/components/ui';
import { Card, CardContent } from '@/components/ui/card';
import { LLM_SERVICE } from '@/utils/constants';
import { AVAILABLE_LLMS, llmDetails } from '@/utils/data';

export const PreviewConfig = () => {
  const { configuration, setConfiguration } = usePersonaStore();

  return (
    <ScrollArea className='h-[calc(100vh-300px)] px-2 py-4'>
      <div className='max-w-4xl'>
        <p className='text-gray-dark text-l mb-3 font-medium'>
          Select the LLM service you want to use to test this persona
        </p>

        <div className='mb-8 space-y-4'>
          {AVAILABLE_LLMS.map((llm) => {
            const details = llmDetails[llm as keyof typeof llmDetails];
            const isSelected = configuration.llm === llm;

            return (
              <Card
                key={llm}
                className={`relative cursor-pointer border transition-all duration-300 select-none ${
                  isSelected
                    ? 'border-primary-500 bg-primary-50/50 ring-primary-500/20 shadow-md ring-1'
                    : 'border-strock-200 hover:border-gray-300 hover:bg-gray-50/30'
                } ${details.name === 'Groq' ? 'pointer-events-none !opacity-70' : ''}`}
                onClick={() => setConfiguration({ llm: llm as LLM_SERVICE })}
              >
                <CardContent className='px-6'>
                  <div className='flex items-center gap-6'>
                    {/* Radio button */}
                    <div className='flex-shrink-0'>
                      <div
                        className={`flex h-6 w-6 items-center justify-center rounded-full border-2 transition-all duration-200 ${
                          isSelected
                            ? 'border-primary-500 bg-primary-500 shadow-sm'
                            : 'border-gray-300 hover:border-gray-400'
                        } `}
                      >
                        {isSelected && <div className='h-3 w-3 rounded-full bg-white' />}
                      </div>
                    </div>

                    <div className='flex min-w-0 items-center gap-5'>
                      <div>
                        <img src={details.icon} alt={details.name} className='h-8 w-8' />
                      </div>
                      <div>
                        <h3 className='text-lg font-semibold text-black'>{details.name}</h3>
                        <p className='text-gray-dark mt-1 max-w-md text-sm leading-relaxed'>
                          {details.description}
                        </p>
                      </div>
                    </div>
                  </div>

                  {details.name === 'Groq' && (
                    <Badge className='absolute top-4 right-4 bg-orange-200/80 text-orange-800'>
                      Coming Soon
                    </Badge>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </ScrollArea>
  );
};
