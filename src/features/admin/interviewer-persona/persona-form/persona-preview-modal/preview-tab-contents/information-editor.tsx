import { usePersonaStore } from '../../hooks/use-persona-store';
import HookFormItem from '@/components/hook-form/HookFormItem';
import { Form, ScrollArea } from '@/components/ui';
import { AutosizeTextarea } from '@/components/ui/autosize-textarea';
import { cn } from '@/libs/utils';
import { previewPersonaSchema, PreviewPersonaType } from '@/validations/personaSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

interface Props {
  persona: BasePersona;
  className?: string;
}

export const InformationEditor = ({ persona, className }: Props) => {
  const { updatePersonaFormData } = usePersonaStore();

  const form = useForm<PreviewPersonaType>({
    resolver: zodResolver(previewPersonaSchema),
    defaultValues: {
      description: persona.description,
      prompt_text: persona.prompt_text,
    },
  });

  return (
    <ScrollArea className={cn('h-[calc(100vh-390px)] py-2', className)}>
      <div>
        <Form {...form}>
          <form className='space-y-6 px-2'>
            <HookFormItem name='description' label='Description'>
              <AutosizeTextarea
                placeholder='Write your persona description here...'
                className='resize-none leading-6'
                onChange={(e) => updatePersonaFormData({ description: e.target.value })}
              />
            </HookFormItem>
            <HookFormItem name='prompt_text' label='Prompt Instruction'>
              <AutosizeTextarea
                placeholder='Write your interview instruction here...'
                className='resize-none leading-6'
                onChange={(e) => updatePersonaFormData({ prompt_text: e.target.value })}
              />
            </HookFormItem>
          </form>
        </Form>
      </div>
    </ScrollArea>
  );
};
