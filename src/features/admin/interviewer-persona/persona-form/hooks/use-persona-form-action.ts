/* eslint-disable @typescript-eslint/no-explicit-any */
import { usePersonaStore } from './use-persona-store';
import { usePersonaPreviewModal } from '@/features/admin/interviewer-persona/persona-form/hooks/use-persona-preview-modal';
import { useCreatePersonaMutation, useUpdatePersonaMutation } from '@/hooks/api/use-persona';
import { useAuthSlice } from '@/hooks/use-auth-slice';
import { PersonaCreationMode } from '@/stores/slices/persona-slice';
import { UseFormReturn } from 'react-hook-form';

export const usePersonaFormAction = (
  form: UseFormReturn<any>,
  options?: { mode: `${PersonaCreationMode}` }
) => {
  const { user } = useAuthSlice();
  const { setMode, updatePersonaFormData } = usePersonaStore();
  const { openModal } = usePersonaPreviewModal();

  const { mutate: createMutate, isPending: isCreating } = useCreatePersonaMutation();
  const { mutate: updateMutate, isPending: isUpdating } = useUpdatePersonaMutation();

  const createPersona = (data: Omit<PersonaCreatePayload, 'created_by'>) => {
    createMutate(
      {
        ...data,
        created_by: user.user_name,
      },
      {
        onSuccess: (response) => {
          const formData = form?.getValues();
          updatePersonaFormData({
            ...formData,
            id: response.id,
          });

          if (options?.mode) {
            setMode(options.mode as PersonaCreationMode);
          }

          openModal();
        },
      }
    );
  };

  const updatePersona = (id: string, data: Partial<BasePersona>) => {
    updateMutate(
      {
        id: id!,
        persona: data,
      },
      {
        onSuccess: (response) => {
          updatePersonaFormData(response);
          openModal();
        },
      }
    );
  };

  return {
    createPersona,
    isCreating,
    updatePersona,
    isUpdating,
  };
};
