import { PersonaSlice } from '@/stores/slices/persona-slice';
import { useStore } from '@/stores/store';
import { useShallow } from 'zustand/react/shallow';

export const usePersonaStore = () => {
  return useStore(
    useShallow((state: PersonaSlice) => ({
      mode: state.mode,
      setMode: state.setMode,

      configuration: state.configuration,
      setConfiguration: state.setConfiguration,

      cvInfo: state.cvInfo,
      setCvInfo: state.setCvInfo,

      personaFormData: state.personaFormData,
      updatePersonaFormData: state.updatePersonaFormData,
      resetPersonaForm: state.resetPersonaForm,
    }))
  );
};
