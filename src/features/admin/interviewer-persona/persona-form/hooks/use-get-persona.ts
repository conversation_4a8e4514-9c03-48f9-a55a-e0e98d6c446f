/* eslint-disable react-hooks/exhaustive-deps */
import { usePersonaStore } from './use-persona-store';
import { usePersonaDetailsQuery } from '@/hooks/api/use-persona';
import { PersonaCreationMode } from '@/stores/slices/persona-slice';
import { useLocation, useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';

export const useGetPersona = () => {
  const pathname = useLocation().pathname;

  const isEditMode = pathname.includes('edit');

  const personaId = isEditMode ? pathname.split('/').pop() : '';

  const { updatePersonaFormData, setMode, resetPersonaForm } = usePersonaStore();
  const navigate = useNavigate();

  const {
    data: persona,
    isLoading,
    isError,
  } = usePersonaDetailsQuery(personaId || '', {
    enabled: isEditMode,
  });

  if (isError) {
    navigate({ to: '/admin/interviewer-persona' });
  }

  useEffect(() => {
    if (!isEditMode) {
      resetPersonaForm();
    }
  }, []);

  useEffect(() => {
    if (persona) {
      setMode(
        (Object.keys(persona.prompt_generation_metadata).length > 0
          ? 'guided'
          : 'manual') as PersonaCreationMode
      );
      updatePersonaFormData(persona);
    }
  }, [persona]);

  return {
    action: isEditMode ? 'edit' : 'create',
    mode: Object.keys(persona?.prompt_generation_metadata ?? {}).length > 0 ? 'guided' : 'manual',
    persona,
    isLoading,
    isError,
  };
};
