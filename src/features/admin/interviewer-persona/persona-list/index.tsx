'use client';

import ListSkeleton from './components/list-skeleton';
import { PersonaCard } from './components/persona-card';
import { RobotIcon } from '@/assets/icons';
import { Main } from '@/components/layout/main';
import { PageHeader } from '@/components/shared/PageHeader';
import { SearchInput } from '@/components/shared/search-input';
import { Button } from '@/components/ui/button';
import { usePersonasInfiniteQuery } from '@/hooks/api/use-persona';
import { useTableState } from '@/hooks/use-table-state';
import { cn } from '@/libs/utils';
import { InfiniteData } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { Grid3X3, List, Plus } from 'lucide-react';
import { useState } from 'react';

export default function InterviewAgentsList() {
  const navigate = useNavigate();

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const { search, setSearch, debouncedSearch } = useTableState();

  const {
    data: personas,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = usePersonasInfiniteQuery({
    search: debouncedSearch,
    limit: 12,
  });

  const handleCreateAgent = () => {
    navigate({ to: '/admin/interviewer-persona/create' });
  };

  const renderContent = (personas?: InfiniteData<PaginatedList<ExtendedPersona>> | undefined) => {
    if (isLoading || !personas) {
      return <ListSkeleton viewMode={viewMode} />;
    }

    const hasPersonas = personas.pages.some((page) => page?.items?.length > 0);

    if (!hasPersonas) {
      return (
        <div className='flex h-[calc(100vh-355px)] w-full flex-col items-center justify-center'>
          <div className='bg-primary-100/80 rounded-2xl p-4'>
            <RobotIcon className='text-primary-600' />
          </div>
          <h3 className='mt-4 text-lg font-semibold text-gray-800'>No Persona Found</h3>
          <p className='text-gray-light mt-1 max-w-md text-center text-sm italic'>
            It looks like there are no interview personas available. Click the "Create Persona"
            button to get started!
          </p>
        </div>
      );
    }

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1, transition: { staggerChildren: 0.1 } }}
        className={`grid gap-6 ${
          viewMode === 'grid'
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1'
        }`}
      >
        {personas.pages.map((page) =>
          page?.items.map((persona) => <PersonaCard key={persona.id} persona={persona} />)
        )}
      </motion.div>
    );
  };

  return (
    <div className='bg-custom-white min-h-screen'>
      <PageHeader title='Interviewer Persona List' />

      <Main className='pt-0'>
        <div className='mb-6 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center'>
          <div className='relative flex-1'>
            <SearchInput className='w-80' value={search} onChange={setSearch} />
          </div>

          <div className='flex items-center gap-2'>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              onClick={() => setViewMode('grid')}
              size={'lg'}
            >
              <Grid3X3 className='h-4 w-4' />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              onClick={() => setViewMode('list')}
              size={'lg'}
            >
              <List className='h-4 w-4' />
            </Button>
          </div>
          <Button onClick={handleCreateAgent} className='shrink-0' size={'lg'}>
            <Plus />
            Create Persona
          </Button>
        </div>

        {renderContent(personas)}

        <div
          className={cn('my-8 flex items-center justify-center', {
            hidden: !hasNextPage,
          })}
        >
          <Button
            onClick={() => fetchNextPage()}
            disabled={!hasNextPage || isFetchingNextPage}
            variant={'outline'}
            className='w-40 rounded-3xl'
          >
            {isFetchingNextPage ? 'Loading...' : hasNextPage ? 'Load More' : ''}
          </Button>
        </div>
      </Main>
    </div>
  );
}
