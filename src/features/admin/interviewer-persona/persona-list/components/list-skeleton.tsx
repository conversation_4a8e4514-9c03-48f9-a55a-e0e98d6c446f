interface Props {
  viewMode: 'grid' | 'list';
}

const ListSkeleton = ({ viewMode }: Props) => {
  return (
    <div>
      {viewMode === 'grid' ? (
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
          {[...Array(6)].map((_, index) => (
            <div key={index} className='h-96 w-full animate-pulse rounded-lg bg-gray-200'></div>
          ))}
        </div>
      ) : (
        <div className='grid grid-cols-1 gap-6'>
          {[...Array(3)].map((_, index) => (
            <div key={index} className='h-72 w-full animate-pulse rounded-lg bg-gray-200'></div>
          ))}
        </div>
      )}
    </div>
  );
};
export default ListSkeleton;
