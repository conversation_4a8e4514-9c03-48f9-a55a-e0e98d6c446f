'use client';

import { Badge } from '@/components/ui';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { getInitials } from '@/utils/helper';
import { useNavigate } from '@tanstack/react-router';
import { motion } from 'framer-motion';
import { Target, UserCircle } from 'lucide-react';
import { useState } from 'react';

const difficultyColors = {
  Beginner: 'bg-green-100 text-green-800 hover:bg-green-200',
  Intermediate: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
  Advance: 'bg-red-100 text-red-800 hover:bg-red-200',
};

interface Props {
  persona: ExtendedPersona;
}

export function PersonaCard({ persona }: Props) {
  const navigate = useNavigate();

  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className='border-border/50 h-full cursor-pointer transition-all duration-200 hover:shadow-sm'>
        <CardHeader className='pb-3'>
          <div className='flex items-start justify-between'>
            <div className='flex items-center space-x-3'>
              <Avatar className='h-12 w-12'>
                <AvatarImage src={'/placeholder.svg'} alt={persona.title} />
                <AvatarFallback className='bg-primary/10 text-primary font-semibold'>
                  {getInitials(persona.title)}
                </AvatarFallback>
              </Avatar>
              <div className='min-w-0 flex-1'>
                <h3 className='text-foreground line-clamp-1 truncate text-lg font-semibold text-wrap'>
                  {persona.title}
                </h3>
                <p className='text-muted-foreground mt-1 flex items-center text-sm'>
                  <UserCircle className='mr-1.5 size-4' />
                  {persona.created_by}
                </p>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className='h-full pt-0'>
          <div className='flex h-full flex-col justify-between space-y-4'>
            {/* Stats Row */}
            <div className='space-y-3'>
              <div className='flex items-center justify-between text-sm'>
                <div className='flex items-center space-x-4'>
                  <div className='text-muted-foreground flex items-center'>
                    <Target className='mr-1 h-4 w-4' />
                    <span className='font-medium'>{persona.attached_job_count}</span>
                    <span className='ml-1'>jobs</span>
                  </div>
                </div>
                {persona.prompt_generation_metadata?.difficulty && (
                  <Badge
                    variant='secondary'
                    className={
                      difficultyColors[
                        persona.prompt_generation_metadata
                          .difficulty as keyof typeof difficultyColors
                      ]
                    }
                  >
                    {persona.prompt_generation_metadata.difficulty}
                  </Badge>
                )}
              </div>

              {/* Persona Description */}
              <div className='flex-1 space-y-2'>
                <div className='flex items-center gap-2'>
                  <span className='text-muted-foreground text-xs font-medium tracking-wide uppercase'>
                    Persona
                  </span>
                  <div className='bg-border h-px flex-1'></div>
                </div>
                <p className='text-muted-foreground line-clamp-3 text-sm leading-relaxed'>
                  {persona.description}
                </p>
              </div>
            </div>

            {/* Action Button */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant='outline'
                size='lg'
                className='mt-2 w-full bg-transparent'
                onClick={() => navigate({ to: `/admin/interviewer-persona/${persona.id}` })}
              >
                See Persona Details
              </Button>
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
