import { Badge } from '@/components/ui';
import { ColumnDef } from '@tanstack/react-table';
import { endOfDay, format, formatDistanceStrict, isBefore } from 'date-fns';

export const taggedJobColumns: ColumnDef<PersonaAttachedJobs>[] = [
  {
    accessorKey: 'title',
    header: 'Job Title',
  },
  {
    accessorKey: 'location',
    header: 'Location',
  },
  {
    accessorKey: 'experience',
    header: 'Experience (years)',
    meta: {
      align: 'center',
    },
    cell: ({ row }) => {
      const { min_exp, max_exp } = row.original;
      if (min_exp || max_exp) return `${min_exp ?? '-'} - ${max_exp ?? '-'}`;
      return '-';
    },
  },
  {
    accessorKey: 'application_start_date',
    header: 'Application Start Date',
    cell: ({ row }) => {
      return row.original.application_start_date
        ? format(new Date(row.original.application_start_date), 'dd MMM, yyyy')
        : '-';
    },
  },
  {
    accessorKey: 'deadline',
    header: 'Deadline',
    cell: ({ row }) => {
      const { application_end_date } = row.original;
      if (!application_end_date) return '-';

      const deadline = endOfDay(new Date(application_end_date));
      const today = new Date();

      const formattedDate = format(deadline, 'd MMM yyyy');

      return (
        <div className='flex items-center gap-2'>
          <div>{formattedDate}</div>
          {application_end_date && (
            <Badge
              variant={isBefore(today, deadline) ? 'secondary' : 'destructive'}
              className='text-xs'
            >
              {isBefore(today, deadline)
                ? `${formatDistanceStrict(deadline, today)} left`
                : 'Expired'}
            </Badge>
          )}
        </div>
      );
    },
  },
];
