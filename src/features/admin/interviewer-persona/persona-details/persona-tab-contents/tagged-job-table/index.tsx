import { taggedJobColumns } from './columns';
import {
  DataTable,
  DataTableContainer,
  DataTableFooter,
  DataTableHeader,
} from '@/components/data-table';
import { SearchInput } from '@/components/shared/search-input';
import TablePagination from '@/components/shared/table-pagination';
import { ScrollArea } from '@/components/ui';
import { useGetJobsByPersonaQuery } from '@/hooks/api/use-persona';
import { useTableState } from '@/hooks/use-table-state';
import { useNavigate, useParams } from '@tanstack/react-router';
import { useMemo } from 'react';

const TaggedJobTable = () => {
  const navigate = useNavigate();
  const { personaId } = useParams({
    from: '/_authenticated/admin/interviewer-persona/$personaId/',
  });

  const { search, setSearch, debouncedSearch, pagination } = useTableState();

  const { data: jobs, isFetching } = useGetJobsByPersonaQuery(personaId || '', {
    search: debouncedSearch,
    limit: pagination.pageSize,
    offset: pagination.offset,
  });

  const columns = useMemo(() => taggedJobColumns, []);

  return (
    <ScrollArea className='h-[calc(100vh-390px)]'>
      <DataTableContainer className='border-none'>
        <DataTableHeader>
          <DataTableHeader.Left>
            <SearchInput value={search} onChange={setSearch} />
          </DataTableHeader.Left>
        </DataTableHeader>
        <DataTable
          data={jobs?.items ?? []}
          columns={columns}
          isLoading={isFetching}
          onRowClick={(row) => navigate({ to: `/admin/jobs/${row.id}` })}
        />
        <DataTableFooter>
          {!isFetching && <TablePagination totalRecords={jobs?.total || 0} />}
        </DataTableFooter>
      </DataTableContainer>
    </ScrollArea>
  );
};
export default TaggedJobTable;
