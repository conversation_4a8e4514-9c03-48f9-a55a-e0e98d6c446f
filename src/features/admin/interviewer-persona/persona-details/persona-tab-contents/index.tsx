import { InterviewInformation } from './interview-information';
import TaggedJobTable from './tagged-job-table';
import { DataTabs } from '@/components/data-tabs';

interface Props {
  persona: ExtendedPersona;
}

const PersonaTabContents = ({ persona }: Props) => {
  const tabContents: TabItem[] = [
    {
      label: 'Interview Information',
      value: 'interview-information',
      content: () => <InterviewInformation persona={persona} />,
    },
    {
      label: 'Tagged Job Post',
      value: 'tagged-job-post',
      content: () => <TaggedJobTable />,
    },
  ];

  return (
    <div>
      <DataTabs items={tabContents} contentHeight='calc(100vh - 395px)' />
    </div>
  );
};
export default PersonaTabContents;
