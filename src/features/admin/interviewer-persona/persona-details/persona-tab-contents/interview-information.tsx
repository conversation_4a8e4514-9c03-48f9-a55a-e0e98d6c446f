import { ScrollArea } from '@/components/ui';
import { cn } from '@/libs/utils';

interface Props {
  persona: BasePersona;
  className?: string;
}

export const InterviewInformation = ({ persona, className }: Props) => {
  const renderMetadata = () => {
    if (Object.keys(persona.prompt_generation_metadata).length === 0) {
      return null;
    }

    return Object.entries(persona.prompt_generation_metadata).map(([key, value]) => {
      if (key === 'keywords') {
        return (
          <div key={key} className='mb-4'>
            <p className='text-gray-dark mb-2 text-lg font-semibold capitalize'>{key}</p>
            <div className='flex flex-wrap gap-2'>
              {(value as string[]).map((keyword: string) => (
                <div
                  key={keyword}
                  className='bg-custom-white text-gray-dark border-strock rounded-md border px-3 py-1 text-sm'
                >
                  {keyword}
                </div>
              ))}
            </div>
          </div>
        );
      }

      if (Object.keys(value).length === 0 || !value) return null;

      return (
        <div key={key} className='mb-4'>
          <p className='text-gray-dark mb-1 text-lg font-semibold capitalize'>{key}</p>
          <p className='text-gray-dark'>{value}</p>
        </div>
      );
    });
  };

  return (
    <ScrollArea className={cn('h-[calc(100vh-390px)] p-6', className)}>
      <div className='max-w-4xl'>
        {renderMetadata()}
        <div className='mb-4'>
          <p className='text-gray-dark mb-1 text-lg font-semibold capitalize'>Description</p>
          <p className='text-gray-dark leading-6'>{persona.description}</p>
        </div>
        <div>
          <p className='text-gray-dark mb-1 text-lg font-semibold capitalize'>Prompt Instruction</p>
          <p className='text-gray-dark leading-6'>{persona.prompt_text}</p>
        </div>
      </div>
    </ScrollArea>
  );
};
