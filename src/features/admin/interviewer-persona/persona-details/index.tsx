import { PersonaPreviewModal } from '../persona-form/persona-preview-modal';
import { PersonaDetailsAction } from './components/persona-details-action';
import PersonaTabContents from './persona-tab-contents';
import { PageHeader } from '@/components/shared/PageHeader';
import { Loader } from '@/components/shared/loader';
import { NotFoundList } from '@/components/shared/not-found-list';
import { usePersonaDetailsQuery } from '@/hooks/api/use-persona';
import { useParams } from '@tanstack/react-router';
import { format } from 'date-fns';

export const PersonaDetails = () => {
  const { personaId } = useParams({
    from: '/_authenticated/admin/interviewer-persona/$personaId/',
  });

  const { data: persona, isLoading, isError } = usePersonaDetailsQuery(personaId || '');

  if (isLoading) {
    return <Loader type='page' />;
  }

  if (isError || !persona) {
    return <NotFoundList />;
  }

  const breadcrumbs = [
    { label: 'Home', href: '/admin' },
    { label: 'Personas', href: '/admin/interviewer-persona' },
    { label: 'Persona Details', href: `/admin/interviewer-persona/${personaId}` },
  ];

  return (
    <div className='bg-custom-white min-h-screen'>
      <PageHeader title='Persona Details' breadcrumbs={breadcrumbs} showNavigation />
      <div className='space-y-6 p-6 pt-0'>
        <section className='border-strock rounded-2xl border bg-white p-6'>
          <div className='flex items-center justify-between'>
            <h1 className='text-2xl text-black'>{persona?.title}</h1>

            {persona && <PersonaDetailsAction persona={persona} />}
          </div>

          {/* Stats */}
          <div className='border-strock my-6 grid grid-cols-3 gap-2 rounded-md border p-4'>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Tagged Jobs</p>
              <p className='text-base font-medium text-black'>{persona.attached_job_count}</p>
            </div>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Created By</p>
              <p className='text-base font-medium text-black'>{persona?.created_by}</p>
            </div>

            <div>
              <p className='text-gray-light mb-1 text-sm'>Created At</p>
              <p className='text-base font-medium text-black'>
                {persona?.created_at ? format(persona?.created_at, 'dd MMM yyyy') : '-'}
              </p>
            </div>
          </div>

          <PersonaTabContents persona={persona} />

          <PersonaPreviewModal skipAction />
        </section>
      </div>
    </div>
  );
};
