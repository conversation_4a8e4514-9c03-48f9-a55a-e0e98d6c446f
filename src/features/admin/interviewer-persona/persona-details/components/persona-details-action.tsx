import { usePersonaPreviewModal } from '../../persona-form/hooks/use-persona-preview-modal';
import { usePersonaStore } from '../../persona-form/hooks/use-persona-store';
import { SimpleDropdown } from '@/components/ui';
import { useDeletePersonaMutation } from '@/hooks/api/use-persona';
import { useNavigate } from '@tanstack/react-router';
import { Eye, SquarePen, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

interface Props {
  persona: ExtendedPersona;
}

export const PersonaDetailsAction = ({ persona }: Props) => {
  const navigate = useNavigate();

  const { updatePersonaFormData } = usePersonaStore();
  const { openModal } = usePersonaPreviewModal();

  const { mutate: deletePersona, isPending } = useDeletePersonaMutation();

  const handleDeletePersona = () => {
    deletePersona(persona.id, {
      onSuccess: () => {
        toast.success('Persona deleted successfully');
        navigate({ to: '/admin/interviewer-persona' });
      },
    });
  };

  const handleOpenPreviewModal = () => {
    updatePersonaFormData({
      id: persona.id,
      description: persona.description,
      prompt_text: persona.prompt_text,
      prompt_generation_metadata: persona.prompt_generation_metadata,
      title: persona.title,
    });
    openModal();
  };

  return (
    <SimpleDropdown
      triggerClassName='bg-custom-white border border-strock size-10 focus-visible:border-strock'
      items={[
        {
          label: 'Edit Persona',
          onClick: () => navigate({ to: `/admin/interviewer-persona/edit/${persona.id}` }),
          icon: <SquarePen />,
        },
        {
          label: 'Preview',
          onClick: handleOpenPreviewModal,
          icon: <Eye />,
        },
        {
          label: 'Remove',
          onClick: handleDeletePersona,
          icon: <Trash2 />,
          disabled: isPending,
        },
      ]}
    />
  );
};
