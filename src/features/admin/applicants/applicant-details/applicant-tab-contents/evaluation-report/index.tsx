import { AnalysisReport } from './analysis-report';
import { Card, CardContent } from '@/components/ui/card';
import { FileText } from 'lucide-react';

interface Props {
  evaluationData: string | null;
  isEvaluationLoading: boolean;
}

export const EvaluationReport = ({ evaluationData, isEvaluationLoading }: Props) => {
  return (
    <div className='h-full overflow-auto p-6'>
      <Card className='h-evaluation-template bg-card overflow-auto border-0 p-0'>
        <CardContent className='space-y-6 p-6'>
          {isEvaluationLoading ? (
            <div className='space-y-4'>
              <div className='h-6 w-3/4 animate-pulse rounded bg-gray-200' />
              <div className='h-4 w-full animate-pulse rounded bg-gray-200' />
              <div className='h-4 w-5/6 animate-pulse rounded bg-gray-200' />
              <div className='h-4 w-4/5 animate-pulse rounded bg-gray-200' />
            </div>
          ) : evaluationData && evaluationData.trim() ? (
            <AnalysisReport rawText={evaluationData} />
          ) : (
            <div className='flex h-120 items-center justify-center'>
              <div className='text-center'>
                <div className='text-gray-light mb-2'>
                  <FileText className='mx-auto size-12' />
                </div>
                <h3 className='text-gray-dark text-lg font-medium'>No Evaluation Available</h3>
                <p className='mt-1 text-sm text-gray-500'>
                  The overall evaluation comment has not been generated yet.
                  <br />
                  Or the candidate did not attend the interview.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
