'use client';

import {
  getRiskBadgeColor,
  getVerdictColor,
  getVerdictIcon,
  parseEvaluation,
} from './utils/helper';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/libs/utils';
import {
  AlertCircle,
  AlertTriangle,
  Brain,
  CheckCircle,
  ChevronRight,
  Eye,
  FileText,
  Flag,
  Goal,
  Lightbulb,
  Shield,
  Sparkles,
  Star,
  Target,
  TrendingDown,
  TrendingUp,
  User,
  XCircle,
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';

export interface EvaluationReportData {
  candidateName: string;
  authenticity: number;
  aiRisk: 'Low' | 'Medium' | 'High';
  tldr: string[];
  swotSection: string;
  strengths: Array<{
    title: string;
    description: string;
  }>;
  weaknesses: Array<{
    title: string;
    description: string;
  }>;
  opportunities: Array<{
    title: string;
    description: string;
  }>;
  threats: Array<{
    title: string;
    description: string;
  }>;
  definingMoments: Array<{
    title: string;
    description: string;
  }>;
  verdict: {
    status: 'Strong' | 'Adequate' | 'Concerning' | 'Needs Review';
    description: string;
  };
}

interface EvaluationReportProps {
  rawText?: string;
}

export function AnalysisReport({ rawText }: EvaluationReportProps) {
  const evaluationData = rawText ? parseEvaluation(rawText) : null;

  if (!evaluationData && rawText) {
    return (
      <div>
        <Card className='from-background to-muted/20 border-0 bg-gradient-to-br'>
          <CardHeader className='pb-6'>
            <div className='flex items-center gap-4'>
              <div className='from-primary/10 to-primary/5 border-primary/10 rounded-2xl border bg-gradient-to-br p-3'>
                <Brain className='text-primary h-7 w-7' />
              </div>
              <div>
                <CardTitle className='text-2xl font-bold text-balance'>
                  AI Interview Evaluation
                </CardTitle>
                <p className='text-muted-foreground mt-1'>Automated Assessment Report</p>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className='from-primary/5 to-primary/10 border-primary/20 items-start gap-4 rounded-2xl border bg-gradient-to-r p-6'>
              <div className='prose max-w-none'>
                <ReactMarkdown>{rawText.replace(/\\n/g, '\n')}</ReactMarkdown>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!evaluationData) {
    return (
      <div className='flex h-120 flex-1 items-center justify-center'>
        <div className='text-center'>
          <div className='text-gray-light mb-2'>
            <FileText className='mx-auto size-12' />
          </div>
          <h3 className='text-gray-dark text-lg font-medium'>No Evaluation Available</h3>
          <p className='text-gray-light mt-1 text-sm'>
            The overall evaluation comment has not been generated yet.
            <br />
            Or the candidate did not attend the interview.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='mx-auto space-y-8'>
      <Card className='from-background via-background to-muted/10 border-strock border bg-gradient-to-br'>
        <CardHeader className='pb-6'>
          <div className='flex items-start justify-between'>
            <div className='flex items-center gap-6'>
              <div className='relative'>
                <div className='from-primary to-primary/80 rounded-3xl bg-gradient-to-br p-5 shadow-lg'>
                  <User className='text-primary-foreground size-6' />
                </div>
                <div className='from-primary to-primary/80 border-background absolute -right-1 -bottom-1 rounded-full border-2 bg-gradient-to-br p-1.5'>
                  <Sparkles className='text-primary-foreground h-4 w-4' />
                </div>
              </div>
              <div>
                <CardTitle className='mb-1 text-2xl font-bold text-balance'>
                  {evaluationData.candidateName}
                </CardTitle>
                <div className='text-muted-foreground flex items-center gap-2 text-sm'>
                  <Eye className='size-4.5' />
                  <span>Candidate Evaluation Report</span>
                </div>
              </div>
            </div>
            <div className='space-y-3 text-right'>
              <Badge className='from-primary/10 to-primary/5 text-primary border-primary/20 bg-gradient-to-r px-4 py-2'>
                <Brain className='mr-2 h-4 w-4' />
                AI Powered Analysis
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className='pt-0'>
          <div className='grid grid-cols-1 gap-8 md:grid-cols-2'>
            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center gap-3'>
                  <div className='bg-primary/10 rounded-lg p-2'>
                    <Star className='text-primary h-5 w-5' />
                  </div>
                  <span className='text-base font-semibold'>Authenticity Score</span>
                </div>
                <span className='text-primary text-3xl font-bold'>
                  {evaluationData.authenticity}/10
                </span>
              </div>
              <Progress value={evaluationData.authenticity * 10} className='bg-muted h-2' />
            </div>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-3'>
                <div className='bg-primary/10 rounded-lg p-2'>
                  <Shield className='text-primary h-5 w-5' />
                </div>
                <span className='text-base font-semibold'>AI Risk Assessment</span>
              </div>
              <Badge
                className={cn(
                  'border-0 px-4 py-2 text-sm font-semibold',
                  getRiskBadgeColor(evaluationData.aiRisk)
                )}
              >
                {evaluationData.aiRisk} Risk
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {evaluationData.tldr.length > 0 && (
        <Card>
          <CardHeader className='pb-6'>
            <CardTitle className='flex items-center gap-4 text-2xl font-bold'>
              <div className='from-primary/15 to-primary/5 border-primary/10 rounded-2xl border bg-gradient-to-br p-3'>
                <Lightbulb className='text-primary h-7 w-7' />
              </div>
              <div>
                <span className='text-balance'>Key Insights</span>
                <p className='text-muted-foreground mt-1 text-sm font-normal'>Executive Summary</p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className='pt-0'>
            <div className='space-y-4'>
              {evaluationData.tldr.map((point, index) => (
                <div
                  key={index}
                  className='group from-muted/80 to-muted/50 flex items-start gap-4 rounded-2xl border-0 bg-gradient-to-r p-6 transition-all duration-200'
                >
                  <div className='bg-primary/10 group-hover:bg-primary/15 rounded-xl p-2 transition-colors'>
                    <ChevronRight className='text-primary h-4 w-4' />
                  </div>
                  <span className='flex-1 text-base leading-relaxed text-pretty'>{point}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader className='pb-6'>
          <CardTitle className='flex items-center gap-4 text-2xl font-bold'>
            <div className='from-primary/15 to-primary/5 border-primary/10 rounded-2xl border bg-gradient-to-br p-3'>
              <Target className='text-primary h-7 w-7' />
            </div>
            <div>
              <span className='text-balance'>SWOT Analysis</span>
              <p className='text-muted-foreground mt-1 text-sm font-normal'>
                Strengths, Weaknesses, Opportunities, and Threats
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className='grid grid-cols-1 gap-4 lg:grid-cols-2'>
          <Card className='group overflow-hidden bg-gradient-to-br from-green-50 to-emerald-50'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center gap-4 text-xl font-bold text-green-700 dark:text-green-300'>
                <div className='rounded-2xl border border-green-300 bg-gradient-to-br from-green-200/80 to-green-100/20 p-3 shadow transition-transform dark:border-green-800/50 dark:bg-green-900/50'>
                  <TrendingUp className='h-6 w-6 text-green-600 dark:text-green-400' />
                </div>
                <div>
                  <span>Strengths</span>
                  <p className='mt-0.5 text-sm font-semibold text-green-600/80 dark:text-green-400/80'>
                    Positive Attributes
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className=''>
              {evaluationData.strengths.length > 0 ? (
                <div className='space-y-3'>
                  {evaluationData.strengths.map((strength, index) => (
                    <div
                      key={index}
                      className='rounded-md border border-l-2 border-emerald-200/80 border-l-emerald-300 bg-white p-3'
                    >
                      <div className='flex items-start gap-3'>
                        <CheckCircle className='mt-0.5 h-4 w-4 flex-shrink-0 text-emerald-500' />
                        <div className='space-y-1'>
                          <div className='text-sm font-semibold text-emerald-800'>
                            {strength.title}
                          </div>
                          <div className='text-sm leading-relaxed text-slate-700'>
                            {strength.description}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='rounded-md border border-dashed border-emerald-200 bg-white p-3 text-center'>
                  <p className='text-sm text-emerald-600 italic'>No strengths identified</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className='group overflow-hidden bg-gradient-to-br from-red-50 to-rose-50'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center gap-4 text-xl font-bold text-red-700 dark:text-red-300'>
                <div className='rounded-2xl border border-red-300 bg-gradient-to-br from-red-200/80 to-red-100/20 p-3 shadow transition-transform dark:border-red-800/50 dark:bg-red-900/50'>
                  <TrendingDown className='h-6 w-6 text-red-600 dark:text-red-400' />
                </div>
                <div>
                  <span>Weaknesses</span>
                  <p className='mt-0.5 text-sm font-semibold text-red-600/80 dark:text-red-400/80'>
                    Areas for Improvement
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className=''>
              {evaluationData.weaknesses.length > 0 ? (
                <div className='space-y-3'>
                  {evaluationData.weaknesses.map((weakness, index) => (
                    <div
                      key={index}
                      className='rounded-md border border-l-2 border-red-200/80 border-l-red-300 bg-white p-3'
                    >
                      <div className='flex items-start gap-3'>
                        <XCircle className='mt-0.5 h-4 w-4 flex-shrink-0 text-red-500' />
                        <div className='space-y-1'>
                          <div className='text-sm font-semibold text-red-800'>{weakness.title}</div>
                          <div className='text-sm leading-relaxed text-slate-700'>
                            {weakness.description}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='rounded-md border border-dashed border-red-200 bg-white p-3 text-center'>
                  <p className='text-sm text-red-600 italic'>No weaknesses identified</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className='group overflow-hidden bg-gradient-to-br from-blue-50 to-cyan-50'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center gap-4 text-xl font-bold text-blue-700 dark:text-blue-300'>
                <div className='rounded-2xl border border-blue-300 bg-gradient-to-br from-blue-200/80 to-blue-100/20 p-3 shadow transition-transform dark:border-blue-800/50 dark:bg-blue-900/50'>
                  <Target className='h-6 w-6 text-blue-600 dark:text-blue-400' />
                </div>
                <div>
                  <span>Opportunities</span>
                  <p className='mt-0.5 text-sm font-semibold text-blue-600/80 dark:text-blue-400/80'>
                    Growth Potential
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className=''>
              {evaluationData.opportunities.length > 0 ? (
                <div className='space-y-3'>
                  {evaluationData.opportunities.map((opportunity, index) => (
                    <div
                      key={index}
                      className='rounded-md border border-l-2 border-blue-200/80 border-l-blue-300 bg-white p-3'
                    >
                      <div className='flex items-start gap-3'>
                        <Goal className='mt-0.5 h-4 w-4 flex-shrink-0 text-blue-500' />
                        <div className='space-y-1'>
                          <div className='text-sm font-semibold text-blue-800'>
                            {opportunity.title}
                          </div>
                          <div className='text-sm leading-relaxed text-slate-700'>
                            {opportunity.description}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='rounded-md border border-dashed border-blue-200 bg-white p-3 text-center'>
                  <p className='text-sm text-blue-600 italic'>No opportunities identified</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className='group overflow-hidden bg-gradient-to-br from-yellow-50 to-amber-50'>
            <CardHeader className='pb-6'>
              <CardTitle className='flex items-center gap-4 text-xl font-bold text-yellow-700 dark:text-yellow-300'>
                <div className='rounded-2xl border border-yellow-300 bg-gradient-to-br from-yellow-200/80 to-yellow-100/20 p-3 shadow transition-transform dark:border-yellow-800/50 dark:bg-yellow-900/50'>
                  <AlertTriangle className='h-6 w-6 text-yellow-600 dark:text-yellow-400' />
                </div>
                <div>
                  <span>Threats</span>
                  <p className='mt-0.5 text-sm font-semibold text-yellow-600/80 dark:text-yellow-400/80'>
                    Risk Factors
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className=''>
              {evaluationData.threats.length > 0 ? (
                <div className='space-y-3'>
                  {evaluationData.threats.map((threat, index) => (
                    <div
                      key={index}
                      className='rounded-md border border-l-2 border-yellow-300/50 border-l-yellow-300 bg-white p-3'
                    >
                      <div className='flex items-start gap-3'>
                        <AlertCircle className='mt-0.5 h-4 w-4 flex-shrink-0 text-yellow-500' />
                        <div className='space-y-1'>
                          <div className='text-sm font-semibold text-yellow-800'>
                            {threat.title}
                          </div>
                          <div className='text-sm leading-relaxed text-slate-700'>
                            {threat.description}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='rounded-md border border-dashed border-yellow-200 bg-white p-3 text-center'>
                  <p className='text-sm text-yellow-600 italic'>No threats identified</p>
                </div>
              )}
            </CardContent>
          </Card>
        </CardContent>
      </Card>

      {evaluationData.definingMoments.length > 0 && (
        <Card>
          <CardHeader className='pb-6'>
            <CardTitle className='flex items-center gap-4 text-2xl font-bold'>
              <div className='from-primary/15 to-primary/5 border-primary/10 rounded-2xl border bg-gradient-to-br p-3'>
                <Flag className='text-primary h-7 w-7' />
              </div>
              <div>
                <span className='text-balance'>Defining Moments</span>
                <p className='text-muted-foreground mt-1 text-sm font-normal'>
                  Key Interview Highlights
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className='pt-0'>
            <div className='ml-2'>
              {evaluationData.definingMoments.map((moment, index) => (
                <div
                  key={index}
                  className='border-primary-100 relative border-l-2 pb-6 pl-10 last:pb-0'
                >
                  <div className='from-primary to-primary/80 border-background absolute top-0 -left-3 h-6 w-6 rounded-full border-4 bg-gradient-to-br shadow-sm' />
                  <div className='from-primary-50/70 to-primary-50/30 border-primary-100 rounded-2xl border bg-gradient-to-br p-6 transition-all duration-200'>
                    <div className='flex items-start gap-4'>
                      <div className='bg-primary/10 rounded-xl p-2'>
                        <Star className='text-primary h-5 w-5' />
                      </div>
                      <div className='flex-1'>
                        <h4 className='mb-3 text-lg font-bold text-balance'>{moment.title}</h4>
                        <p className='text-muted-foreground leading-relaxed text-pretty'>
                          {moment.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <div
        className={cn(
          'flex items-start gap-6 rounded-3xl border p-8',
          getVerdictColor(evaluationData.verdict.status)
        )}
      >
        <div
          className={cn('rounded-2xl border border-inherit bg-blue-100 p-3 dark:bg-black/20', {
            'bg-green-100': evaluationData.verdict.status === 'Strong',
            'bg-orange-100': evaluationData.verdict.status === 'Adequate',
            'bg-red-100': evaluationData.verdict.status === 'Concerning',
          })}
        >
          {getVerdictIcon(evaluationData.verdict.status)}
        </div>
        <div className='flex-1'>
          <div className='mb-4 flex items-center gap-3'>
            <h3 className='text-2xl font-bold text-balance'>Final Verdict</h3>
            <Badge className='border bg-white px-3 py-1 font-bold text-inherit shadow dark:bg-black/20'>
              {evaluationData.verdict.status}
            </Badge>
          </div>
          <p className='text-base leading-relaxed text-pretty opacity-90'>
            {evaluationData.verdict.description}
          </p>
        </div>
      </div>
    </div>
  );
}
