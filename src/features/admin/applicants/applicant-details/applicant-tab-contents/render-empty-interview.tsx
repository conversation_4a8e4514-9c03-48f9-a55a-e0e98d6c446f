import { Bot } from 'lucide-react';

export const RenderEmptyInterview = () => {
  return (
    <div className='flex h-full flex-1 flex-col items-center justify-center'>
      <div className='flex flex-col items-center justify-center gap-2'>
        <div className='rounded-full bg-gray-100 p-3'>
          <Bot className='size-10 text-gray-500' />
        </div>
        <h3 className='text-lg font-medium text-gray-900'>No interview conversation available</h3>
        <p className='text-center text-sm text-gray-500'>
          The candidate might not attend the interview yet
        </p>
      </div>
    </div>
  );
};
