import { ApplicantCV } from './applicant-cv';
import { EvaluationReport } from './evaluation-report';
import { InterviewDetails } from './interview-details';
import { DataTabs } from '@/components/data-tabs';
import { Button } from '@/components/ui/button';
import { usePreviewApplicantCVQuery } from '@/hooks/api/use-applicant';
import { Download } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface Props {
  applicantData: ApplicantDetails;
  isLoading: boolean;
}

export const ApplicantTabContents = ({ applicantData, isLoading }: Props) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const interviews = applicantData?.interviews || [];
  const latestInterview = interviews.length > 0 ? interviews[interviews.length - 1] : null;

  const {
    data: pdfLink,
    isLoading: isPdfLoading,
    isError: isPdfError,
  } = usePreviewApplicantCVQuery(applicantData?.cv_link ?? '');

  console.log('CV link', applicantData.cv_link);
  console.log('PDF link', pdfLink);

  const handleDownloadCV = async () => {
    if (pdfLink) {
      try {
        const response = await fetch(pdfLink.url);
        const blob = await response.blob();

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        const fileName = applicantData?.full_name
          ? applicantData.full_name.replace(/\s+/g, '_') + '_CV.pdf'
          : 'applicant_cv';
        link.download = fileName;
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        toast.success('PDF downloaded successfully', {
          duration: 1000,
        });
      } catch (_error) {
        toast.error('Failed to download PDF');
      }
    }
  };

  const tabConfig: TabItem[] = [
    {
      label: 'Evaluation Report',
      value: 'evaluation-report',
      content: () => (
        <EvaluationReport
          evaluationData={latestInterview?.evaluation?.evaluation_data ?? null}
          isEvaluationLoading={isLoading}
        />
      ),
    },
    {
      label: "Applicant's CV",
      value: 'applicant-cv',
      content: () => (
        <ApplicantCV
          cvLink={applicantData?.cv_link}
          pdfLink={pdfLink}
          isLoading={isPdfLoading}
          isError={isPdfError}
        />
      ),
    },
    {
      label: 'Interview Details',
      value: 'interview-details',
      content: () => (
        <InterviewDetails
          candidateName={applicantData.full_name ?? 'Unknown'}
          interviews={latestInterview}
        />
      ),
    },
  ];

  const isApplicantCvTabActive = activeTabIndex === 1;

  return (
    <div className='relative'>
      {isApplicantCvTabActive && (
        <Button
          onClick={handleDownloadCV}
          disabled={!pdfLink || isPdfLoading}
          variant='outline'
          size='lg'
          className='text-md absolute top-0 right-0 z-10 shrink-0'
        >
          <Download className='size-5' />
          Download CV
        </Button>
      )}

      <div className='pr-0'>
        <DataTabs items={tabConfig} onChange={setActiveTabIndex} stickyHeader />
      </div>
    </div>
  );
};
