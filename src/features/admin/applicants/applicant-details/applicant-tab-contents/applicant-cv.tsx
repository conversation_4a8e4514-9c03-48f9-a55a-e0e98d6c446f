import PreviewPdf from '@/components/shared/preview-pdf';
import { FileUser } from 'lucide-react';

interface Props {
  cvLink?: string;
  pdfLink?: PreviewCVResponse;
  isLoading?: boolean;
  isError?: boolean;
}

export const ApplicantCV = ({ cvLink, pdfLink, isLoading: isPdfLoading, isError }: Props) => {
  if (!cvLink) {
    return (
      <div className='flex h-144 items-center justify-center'>
        <div className='text-center'>
          <div className='text-gray-light mb-2'>
            <FileUser className='mx-auto size-12' />
          </div>
          <h3 className='text-gray-dark text-lg font-medium'>No CV Available</h3>
          <p className='mt-1 text-sm text-gray-500'>The candidate did not upload a CV.</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className='flex h-full flex-col items-center justify-center p-6'>
        <p className='text-red-500'>Failed to load CV preview</p>
        <p className='text-xs' aria-label='error'>
          Please try again later
        </p>
      </div>
    );
  }

  return (
    <div className='h-full'>
      {pdfLink?.url ? (
        <div className='bg-custom-white overflow-y-auto'>
          <PreviewPdf
            fileUrl={pdfLink.url}
            isLoading={false}
            isPreviewDataLoading={isPdfLoading}
            scale={0.75}
            className='bg-custom-white flex-1 overflow-y-auto'
            styles={{
              height: 'calc(100vh - 335px)',
            }}
          />
        </div>
      ) : (
        // Loader
        <div className='bg-custom-white relative animate-pulse rounded-2xl p-6'>
          <div className='mx-auto h-[calc(100vh-335px)] max-w-[600px] animate-pulse rounded-2xl bg-white' />
        </div>
      )}
    </div>
  );
};
