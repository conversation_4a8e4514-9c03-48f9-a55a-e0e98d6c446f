import { Ava<PERSON>, Avatar<PERSON><PERSON>back, ScrollArea } from '@/components/ui';
import { cn } from '@/libs/utils';
import { JOB_CANDIDATE_STATUS } from '@/utils/constants';
import { Bot, MessageSquare } from 'lucide-react';

interface QnA {
  question: string;
  answer: string;
  chat_id?: string;
}

interface ChatMessage {
  id: string;
  sender: 'ai' | 'candidate';
  content: string;
}

interface Props {
  interviews?: {
    id: string;
    status: `${JOB_CANDIDATE_STATUS}`;
    interview_date: string;
    qa_array: QnA[];
    evaluation: {
      id: string;
      interview_id: string;
      evaluation_data: string;
    };
  } | null;
  candidateName: string;
}

export const InterviewDetails = ({ interviews, candidateName }: Props) => {
  if (!interviews || !interviews.qa_array) {
    return (
      <div className='flex h-144 items-center justify-center'>
        <div className='text-center'>
          <div className='text-gray-light mb-2'>
            <MessageSquare className='mx-auto size-12' />
          </div>
          <h3 className='text-gray-dark text-lg font-medium'>No Transcript Available</h3>
          <p className='mt-1 text-sm text-gray-500'>
            This interview transcript is not yet available or is still being precessed.
            <br />
            Please check back later.
          </p>
        </div>
      </div>
    );
  }

  function transformQAData(qaArray: QnA[]): ChatMessage[] {
    const chatMessages: ChatMessage[] = [];
    let messageId = 1;

    qaArray.forEach((item) => {
      if (item.question && item.question.trim() !== '') {
        chatMessages.push({
          id: messageId.toString(),
          sender: 'ai',
          content: item.question,
        });
        messageId++;
      }

      if (item.answer && item.answer.trim() !== '') {
        chatMessages.push({
          id: messageId.toString(),
          sender: 'candidate',
          content: item.answer,
        });
        messageId++;
      }
    });

    return chatMessages;
  }

  return (
    <div className='h-full space-y-6 overflow-auto'>
      <ScrollArea className='bg-custom-white/30 flex-1'>
        <div className='mx-auto max-w-4xl px-6 py-8'>
          {interviews.qa_array.length > 0 ? (
            <div className='space-y-8'>
              <div className='mb-8'>
                <h3 className='mb-2 text-xl font-bold text-black'>Interview Transcript</h3>
                <p className='text-gray-light'>Recorded via Previa</p>
              </div>

              <div className='relative'>
                <div className='absolute top-0 bottom-0 left-8 w-px bg-gray-200'></div>

                <div className='space-y-8'>
                  {transformQAData(interviews.qa_array).map((message) => (
                    <div key={message.id} className='relative flex items-start gap-6'>
                      <div className='relative z-10 flex-shrink-0'>
                        <Avatar className='h-16 w-16 border-4 border-white font-medium shadow-sm'>
                          <AvatarFallback
                            className={
                              message.sender === 'ai'
                                ? 'text-primary-700 bg-blue-100'
                                : 'text-gray-dark bg-gray-100'
                            }
                          >
                            {message.sender === 'ai' ? (
                              <Bot className='h-6 w-6' />
                            ) : (
                              candidateName
                                .split(' ')
                                .map((n) => n[0])
                                .join('')
                            )}
                          </AvatarFallback>
                        </Avatar>
                        {message.sender === 'ai' && (
                          <div className='absolute -right-1 -bottom-1 flex h-5 w-5 items-center justify-center rounded-full bg-blue-600'>
                            <Bot className='h-3 w-3 text-white' />
                          </div>
                        )}
                      </div>

                      <div className='min-w-0 flex-1 pb-8'>
                        <div className='mb-3 flex items-center gap-3'>
                          <h4 className='font-semibold text-gray-900'>
                            {message.sender === 'ai' ? 'Previa' : candidateName}
                          </h4>
                        </div>

                        <div
                          className={cn(
                            'rounded-lg border border-gray-100 bg-white p-6 shadow-sm',
                            {
                              'from-primary-500 to-primary-600 bg-gradient-to-bl':
                                message.sender === 'ai',
                            }
                          )}
                        >
                          <p
                            className={cn(
                              'text-gray-dark font-mono leading-relaxed font-medium text-pretty',
                              {
                                'text-white': message.sender === 'ai',
                              }
                            )}
                          >
                            {message.content}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className='py-20 text-center'>
              <div className='inline-block rounded-2xl border border-gray-200 bg-white p-6 shadow-sm'>
                <MessageSquare className='mx-auto mb-6 h-16 w-16 text-gray-400' />
                <h3 className='mb-3 text-2xl font-bold text-gray-900'>No transcripts available</h3>
                <p className='max-w-md text-gray-600'>
                  This interview transcript is not yet available or is still being processed. Please
                  check back later.
                </p>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};
