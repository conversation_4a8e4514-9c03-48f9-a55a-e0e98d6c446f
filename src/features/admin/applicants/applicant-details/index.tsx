import { ApplicantTabContents } from './applicant-tab-contents';
import { PageHeader } from '@/components/shared/PageHeader';
import { Loader } from '@/components/shared/loader';
import { Badge } from '@/components/ui';
import { useApplicantDetailsQuery } from '@/hooks/api/use-applicant';
import { getCandidateBadgeVariant } from '@/utils/helper';
import { useParams } from '@tanstack/react-router';
import { format } from 'date-fns';
import { Mail } from 'lucide-react';

const ApplicantDetails = () => {
  const { applicantId } = useParams({ from: '/_authenticated/admin/applicants/$applicantId/' });

  const { data: applicantData, isLoading, isError } = useApplicantDetailsQuery(applicantId);

  if (isLoading) {
    return <Loader type='page' />;
  }
  if (isError || !applicantData) {
    return <div>Error loading applicant details</div>;
  }

  const breadcrumbs = [
    { label: 'Home', href: '/admin' },
    { label: 'Applicants', href: '/admin/applicants' },
    { label: 'Applicant Details', href: `/admin/applicants/${applicantId}` },
  ];

  return (
    <div className='bg-custom-white min-h-screen'>
      <PageHeader title='Applicant Details' breadcrumbs={breadcrumbs} showNavigation />
      <div className='space-y-6 p-6 pt-0'>
        <section className='border-strock rounded-2xl border bg-white p-6'>
          {/* Header */}
          <div className='flex items-center justify-between'>
            <div>
              <div className='mb-2 flex items-center gap-2'>
                <h1 className='text-2xl text-black'>{applicantData?.full_name}</h1>
              </div>

              <div className='text-gray-light flex items-center gap-1'>
                <Mail className='size-4' />
                <p>{applicantData?.email}</p>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className='border-strock my-6 grid grid-cols-5 gap-2 rounded-md border p-4'>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Applied for</p>
              <p className='text-base font-medium text-black'>{applicantData?.job?.title}</p>
            </div>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Apply date</p>
              <p className='text-base font-medium text-black'>
                {applicantData?.apply_date_time
                  ? format(applicantData.apply_date_time, 'dd MMM yyyy')
                  : '-'}
              </p>
            </div>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Interview date</p>
              <p className='text-base font-medium text-black'>
                {applicantData?.interviews?.[0]?.interview_date
                  ? format(applicantData?.interviews?.[0]?.interview_date, 'dd MMM yyyy')
                  : '-'}
              </p>
            </div>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Status</p>
              <Badge
                variant={getCandidateBadgeVariant(applicantData?.interviews?.[0]?.status)}
                className='capitalize'
              >
                {applicantData?.interviews?.[0]?.status || 'N/A'}
              </Badge>
            </div>
            <div>
              <p className='text-gray-light mb-1 text-sm'>Years of Experience</p>
              <p className='text-base font-medium text-black'>
                {applicantData?.years_of_experience ?? '-'}
              </p>
            </div>
          </div>
          <ApplicantTabContents applicantData={applicantData} isLoading={isLoading} />
        </section>
      </div>
    </div>
  );
};

export default ApplicantDetails;
