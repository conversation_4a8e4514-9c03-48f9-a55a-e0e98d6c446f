import { ConfirmationModalInput } from '@/components/shared/confirmation-modal-input';
import { SimpleDropdown } from '@/components/ui';
import { useDeleteApplicantMutation } from '@/hooks/api/use-applicant';
import { useResendInvitationMutation } from '@/hooks/api/use-interview';
import { Forward, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Props {
  applicant: Applicant;
  jobId: string;
}

export const ApplicantListAction = ({ applicant, jobId }: Props) => {
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isResendConfirmModalOpen, setIsResendConfirmModalOpen] = useState(false);

  const { mutate: resendInvitation, isPending } = useResendInvitationMutation();
  const { mutate: deleteApplicant, isPending: isDeleting } = useDeleteApplicantMutation();

  const handleResendClick = () => {
    setIsResendConfirmModalOpen(true);
  };

  const handleResendInvitation = async () => {
    if (!jobId) return;

    resendInvitation({ candidate_email: applicant.email, job_id: jobId! });
    setIsResendConfirmModalOpen(false);
  };

  const handleRemoveClick = () => {
    setIsConfirmModalOpen(true);
  };

  const handleDeleteApplicant = async () => {
    await deleteApplicant(applicant.id);
    setIsConfirmModalOpen(false);
  };

  return (
    <>
      <SimpleDropdown
        items={[
          {
            label: 'Resend Link',
            onClick: handleResendClick,
            icon: <Forward />,
            disabled: isPending,
          },
          {
            label: 'Remove',
            labelClassName: 'text-red-500',
            onClick: handleRemoveClick,
            icon: <Trash2 className='text-red-400' />,
            disabled: isDeleting,
          },
        ]}
      />
      <ConfirmationModalInput
        title={`Resend Invitation`}
        description={`Are you sure you want to resend the invitation link to ${applicant.full_name}? This will send a new interview invitation email.`}
        onConfirm={handleResendInvitation}
        open={isResendConfirmModalOpen}
        onOpenChange={setIsResendConfirmModalOpen}
        showWarning
        confirmationText='RESEND'
        confirmationPlaceholder='Enter RESEND to confirm resending'
        iconClassName='size-6 text-blue-600 dark:text-blue-400'
        iconWrapperClassName='bg-blue-100 dark:bg-blue-900/20'
        confirmButtonClassName='bg-blue-700'
      />
      <ConfirmationModalInput
        title={`Remove Applicant`}
        description={`Are you sure you want to remove ${applicant.full_name} from applicants? This action will permanently remove the user data from the system.`}
        onConfirm={handleDeleteApplicant}
        open={isConfirmModalOpen}
        onOpenChange={setIsConfirmModalOpen}
        showWarning
        confirmationText='DELETE'
        confirmationPlaceholder='Enter DELETE to confirm deletion'
      />
    </>
  );
};
