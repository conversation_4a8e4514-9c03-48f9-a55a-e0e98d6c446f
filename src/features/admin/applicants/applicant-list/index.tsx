import { applicantColumns } from './columns';
import { ApplicantStatusFilter } from './components/applicant-status-filter';
import {
  DataTable,
  DataTableContainer,
  DataTableFooter,
  DataTableHeader,
} from '@/components/data-table';
import { PageHeader } from '@/components/shared/PageHeader';
import { SearchInput } from '@/components/shared/search-input';
import TablePagination from '@/components/shared/table-pagination';
import { Button } from '@/components/ui';
import { useApplicantsQuery, useExportApplicantsMutation } from '@/hooks/api/use-applicant';
import { useTableState } from '@/hooks/use-table-state';
import { useNavigate } from '@tanstack/react-router';
import { FileDown } from 'lucide-react';
import { useMemo } from 'react';

export default function ApplicantList() {
  const navigate = useNavigate();

  const { search, setSearch, debouncedSearch, pagination, filters, updateFilter } = useTableState({
    initialFilters: {
      status: 'all',
    },
  });
  const { mutate: exportApplicants, isPending: isExporting } = useExportApplicantsMutation();

  const { data: applicants, isLoading } = useApplicantsQuery(
    {
      limit: pagination.pageSize,
      offset: pagination.offset,
      search: debouncedSearch,
      interview_status: filters.status !== 'all' ? filters.status : undefined,
    },
    { enabled: true }
  );

  const exportApplicantsExcel = async () => {
    exportApplicants({
      limit: pagination.pageSize,
      offset: pagination.offset,
      search: debouncedSearch,
      interview_status: filters.status !== 'all' ? filters.status : undefined,
    });
  };

  const columns = useMemo(() => applicantColumns, []);

  return (
    <div className='bg-custom-white min-h-screen p-0'>
      <PageHeader title='Applicants List' />
      <div className='p-6 pt-0'>
        <DataTableContainer>
          <DataTableHeader>
            <DataTableHeader.Left>
              <div className='flex items-center gap-3'>
                <SearchInput value={search} onChange={setSearch} />
                <ApplicantStatusFilter
                  value={filters.status}
                  onChange={(status) => updateFilter('status', status || 'all')}
                />
              </div>
            </DataTableHeader.Left>
            <DataTableHeader.Right>
              <Button
                variant='secondary'
                onClick={exportApplicantsExcel}
                disabled={isExporting}
                loading={isExporting}
                loadingText='Exporting...'
              >
                <FileDown className='size-4' />
                Export as Excel
              </Button>
            </DataTableHeader.Right>
          </DataTableHeader>
          <DataTable
            data={applicants?.items || []}
            columns={columns}
            isLoading={isLoading}
            onRowClick={(row) => navigate({ to: `/admin/applicants/${row.id}` })}
          />
          <DataTableFooter>
            <TablePagination totalRecords={applicants?.total || 0} />
          </DataTableFooter>
        </DataTableContainer>
      </div>
    </div>
  );
}
