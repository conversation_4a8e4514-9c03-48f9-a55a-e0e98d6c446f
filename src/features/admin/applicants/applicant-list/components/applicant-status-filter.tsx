import { Button } from '@/components/ui';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';
import { JOB_CANDIDATE_STATUS } from '@/utils/constants';
import { ChevronDown } from 'lucide-react';
import { CircleIcon } from 'lucide-react';

interface ApplicantStatusFilterProps {
  value?: string;
  onChange: (status: string | undefined) => void;
}

const STATUS_ITEMS = [
  { value: 'all', label: 'All' },
  ...Object.values(JOB_CANDIDATE_STATUS).map((s) => ({
    value: s,
    label: s.charAt(0).toUpperCase() + s.slice(1).replace('_', ' '),
  })),
];

export function ApplicantStatusFilter({ value, onChange }: ApplicantStatusFilterProps) {
  const display = value ? value.charAt(0).toUpperCase() + value.slice(1).replace('_', ' ') : 'All';

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='secondary'
          size='sm'
          className='flex min-w-[130px] items-center gap-1 py-5'
        >
          <span className='text-gray-dark'>Status: {display}</span>
          <ChevronDown className='size-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className='font-manrope min-w-[130px]'>
        <DropdownMenuRadioGroup
          value={value ?? ''}
          onValueChange={(val) => onChange(val === '' ? undefined : val)}
        >
          {STATUS_ITEMS.map(({ value: val, label }) => (
            <DropdownMenuRadioItem
              key={val}
              value={val}
              className='group relative flex cursor-pointer items-center pl-8 data-[state=checked]:bg-blue-100 data-[state=checked]:text-blue-700'
            >
              {value === val && (
                <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>
                  <CircleIcon
                    className='status-dot fill-primary-500 text-primary-500 size-2'
                    strokeWidth={2}
                    fill='#5c92fa'
                  />
                </span>
              )}
              {label}
            </DropdownMenuRadioItem>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
