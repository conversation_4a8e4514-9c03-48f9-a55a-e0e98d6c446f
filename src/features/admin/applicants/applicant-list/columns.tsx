import { ApplicantListAction } from './applicant-list-action';
import { Badge } from '@/components/ui';
import { JOB_CANDIDATE_STATUS } from '@/utils/constants';
import { snakeToTitleCase, getCandidateBadgeVariant } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';

export const applicantColumns: ColumnDef<Applicant>[] = [
  {
    accessorKey: 'full_name',
    header: 'Applicant Name',
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'job_title',
    header: 'Job Post',
  },
  {
    accessorKey: 'interview_status',
    header: 'Interview Status',
    cell: ({ row }) => {
      return (
        <Badge
          key={row.original.id}
          variant={getCandidateBadgeVariant(row.original.interview_status as JOB_CANDIDATE_STATUS)}
        >
          {row.original.interview_status ? snakeToTitleCase(row.original.interview_status) : '-'}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'interview_date',
    header: 'Interview Date',
    cell: ({ row }) => {
      return row.original.interview_date
        ? new Date(row.original.interview_date).toLocaleDateString('en-US', {
            day: '2-digit',
            month: 'long',
            year: 'numeric',
          })
        : '-';
    },
  },
  {
    accessorKey: 'actions',
    header: 'Actions',
    meta: {
      aligh: 'right',
    },
    cell: ({ row }) => {
      return (
        <div onClick={(e) => e.stopPropagation()}>
          <ApplicantListAction applicant={row.original} jobId={row.original.job_id} />
        </div>
      );
    },
  },
];
