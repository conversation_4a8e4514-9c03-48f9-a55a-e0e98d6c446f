import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>er,
  CardT<PERSON>le,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui';

const jobData = [
  {
    title: 'Product Manager L-2',
    totalApplicants: 23,
    recommended: 4,
    timeLeft: '28d',
  },
  {
    title: 'UI/UX Designer',
    totalApplicants: 43,
    recommended: 9,
    timeLeft: '12d',
  },
  {
    title: 'React Developer',
    totalApplicants: 226,
    recommended: 122,
    timeLeft: '2d',
  },
  {
    title: 'Python Developer',
    totalApplicants: 124,
    recommended: 32,
    timeLeft: '1d',
  },
];

export function ActiveJobsTable() {
  return (
    <Card className='h-full'>
      <CardHeader className='flex flex-row items-center justify-between'>
        <CardTitle className='font-sf-pro-bold text-xl font-medium'>Active Job Posts</CardTitle>
        <Button variant='secondary' size='sm' className='hover:bg-primary-100 text-black'>
          View all
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow className='text-xs'>
              <TableHead className='text-previa-neutral-500' colSpan={2}>
                Job title
              </TableHead>
              <TableHead className='text-previa-neutral-500 text-right'>Total Candidates</TableHead>
              <TableHead className='text-previa-neutral-500 text-right'>Recommended</TableHead>
              <TableHead className='text-previa-neutral-500 text-right'>Time left</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {jobData.map((job) => (
              <TableRow key={job.title}>
                <TableCell className='h-12 border-b border-neutral-200 font-medium' colSpan={2}>
                  {job.title}
                </TableCell>
                <TableCell className='border-b border-neutral-200 text-right'>
                  {job.totalApplicants}
                </TableCell>
                <TableCell className='border-b border-neutral-200 text-right'>
                  {job.recommended}
                </TableCell>
                <TableCell className='border-b border-neutral-200 text-right'>
                  {job.timeLeft}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
