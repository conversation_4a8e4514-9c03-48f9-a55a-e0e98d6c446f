import JobInformation from './components/job-information';
import { JobNotFound } from './components/job-not-found';
import { OtpVerificationModal } from './components/otp-verification-modal';
import ResumeUploader from './components/resume-uploader';
import HookFormItem from '@/components/hook-form/HookFormItem';
import { Loader } from '@/components/shared/loader';
import { Button, Form, FormLabel, Input } from '@/components/ui';
import { useCompleteProfileMutation, useProfileCreateMutation } from '@/hooks/api/use-candidate';
import { useJobQuery } from '@/hooks/api/use-job';
import { useCandidateStore } from '@/stores/candidate-store';
import { isDisposableEmail } from '@/utils/email-validation';
import {
  ApplicationDetailsData,
  applicationDetailsSchema,
  EmailVerificationData,
  emailVerificationSchema,
} from '@/validations/candidateSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useNavigate, useParams } from '@tanstack/react-router';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const JobApplicationForm = () => {
  const { jobId } = useParams({ from: '/(candidate)/job-application/$jobId/' });
  const navigate = useNavigate();

  const [cv, setCv] = useState<string | null>('');
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [currentStep, setCurrentStep] = useState<'verification' | 'details'>('verification');
  const [isEmailVerifying, setIsEmailVerifying] = useState(false);

  const { data: jobData, isLoading, isError } = useJobQuery(jobId);

  const { mutate: createProfile, isPending: isCreatingProfile } = useProfileCreateMutation();
  const { mutate: completeProfile, isPending: isCompletingProfile } = useCompleteProfileMutation();

  const { candidateId, setCandidateId, candidateDetails, setCandidateDetails, setJobTitle } =
    useCandidateStore();

  const emailVerificationForm = useForm<EmailVerificationData>({
    resolver: zodResolver(emailVerificationSchema),
    defaultValues: {
      full_name: '',
      email: '',
      phone_number: '',
    },
  });

  useEffect(() => {
    if (candidateDetails.name) {
      emailVerificationForm.setValue('full_name', candidateDetails.name);
      emailVerificationForm.setValue('email', candidateDetails.email);
      emailVerificationForm.setValue('phone_number', candidateDetails.phone);
      setCurrentStep('details');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [candidateDetails.name]);

  const applicationDetailsForm = useForm({
    resolver: zodResolver(applicationDetailsSchema),
    defaultValues: {
      address: '',
      years_of_experience: '',
    },
  });

  const handleEmailVerificationProceed = async () => {
    const isValid = await emailVerificationForm.trigger();
    if (isValid) {
      const formData = emailVerificationForm.getValues();

      // TODO: off for IUT interview
      // if (!isBusinessEmail(formData.email)) {
      //   toast.error(
      //     'Personal domains (e.g., gmail.com, yahoo.com, hotmail.com) are not accepted. Please use a business email address.'
      //   );
      //   return;
      // }

      if (import.meta.env.MODE === 'production') {
        setIsEmailVerifying(true);
        const isSpamMail = await isDisposableEmail(formData.email);
        setIsEmailVerifying(false);

        if (isSpamMail) {
          toast.error('Disposable emails are not accepted. Please use a business email address.');
          return;
        }
      }

      createProfile(
        {
          jobId,
          payload: {
            email: formData.email,
            full_name: formData.full_name,
            phone_number: formData.phone_number,
          },
        },
        {
          onSuccess: (data) => {
            setCandidateId(data.id);
            setShowVerificationModal(true);
          },
        }
      );
    }
  };

  const handleApplicationDetailsSubmit = (data: ApplicationDetailsData) => {
    if (!cv) {
      toast.error('Please upload your CV');
      return;
    }

    setJobTitle(jobData?.title || null);

    completeProfile(
      {
        candidateId: candidateId ?? '',
        payload: {
          address: data.address,
          years_of_experience: data.years_of_experience,
          cv_link: cv,
        },
      },
      {
        onSuccess: () => {
          emailVerificationForm.reset();
          applicationDetailsForm.reset();
          setCurrentStep('verification');
          setCv('');
          navigate({ to: '/interview-onboarding' });
        },
      }
    );
  };

  if (isLoading) {
    return <Loader type='page' />;
  }

  if (isError) {
    return <JobNotFound />;
  }

  return (
    <div>
      {jobData && <JobInformation job={jobData} />}
      <section className='mx-auto my-10 max-w-200 px-6 md:px-0'>
        <h3 className='text-gray-dark mb-6 text-2xl'>Application Form</h3>
        <Form {...emailVerificationForm}>
          <form
            className='w-full space-y-5'
            onSubmit={emailVerificationForm.handleSubmit(handleEmailVerificationProceed)}
          >
            <HookFormItem
              name='full_name'
              label='Full Name'
              isRequired
              labelClassName='font-medium'
            >
              <Input placeholder='Enter your full name' disabled={currentStep === 'details'} />
            </HookFormItem>

            <div className='grid grid-cols-1 items-baseline gap-4 md:grid-cols-2'>
              <HookFormItem name='email' label='Email' isRequired labelClassName='font-medium'>
                <Input
                  placeholder='Enter your business email'
                  disabled={currentStep === 'details'}
                />
              </HookFormItem>

              <HookFormItem
                name='phone_number'
                label='Phone'
                isRequired
                labelClassName='font-medium'
              >
                <Input
                  placeholder='Enter your phone number'
                  disabled={currentStep === 'details'}
                  type='number'
                />
              </HookFormItem>
            </div>
            {currentStep === 'verification' && (
              <Button
                className='w-38'
                type='submit'
                loading={isCreatingProfile || isEmailVerifying}
              >
                Proceed
              </Button>
            )}
          </form>
        </Form>

        {currentStep === 'details' && (
          <Form {...applicationDetailsForm}>
            <form
              className='mt-5 w-full space-y-5'
              onSubmit={applicationDetailsForm.handleSubmit(handleApplicationDetailsSubmit)}
            >
              <HookFormItem name='address' label='Address' isRequired labelClassName='font-medium'>
                <Input placeholder='Enter your address' />
              </HookFormItem>

              <HookFormItem
                name='years_of_experience'
                label='Years of experience'
                isRequired
                labelClassName='font-medium'
              >
                <Input type='number' placeholder='Enter your years of experience' />
              </HookFormItem>

              <div>
                <FormLabel className='mb-3'>Resume/CV</FormLabel>
                <ResumeUploader cv={cv} setCV={setCv} jobId={jobId} />
                <p className='text-gray-light mt-3 text-sm'>Only support PDF and DOC files</p>
              </div>
              <Button type='submit' className='w-40' loading={isCompletingProfile}>
                Submit application
              </Button>
            </form>
          </Form>
        )}
      </section>

      {showVerificationModal && (
        <OtpVerificationModal
          isOpen={showVerificationModal}
          onClose={() => setShowVerificationModal(false)}
          onVerify={() => {
            setShowVerificationModal(false);
            setCurrentStep('details');
            setCandidateDetails(
              emailVerificationForm.getValues('full_name'),
              emailVerificationForm.getValues('email'),
              emailVerificationForm.getValues('phone_number')
            );
          }}
        />
      )}
    </div>
  );
};
export default JobApplicationForm;
