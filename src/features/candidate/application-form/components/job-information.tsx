interface Props {
  job: PreviewJob;
}

const JobInformation = ({ job }: Props) => {
  const getJobLevel = (maxExp: number) => {
    if (maxExp <= 2) {
      return 'Entry Level';
    }
    if (maxExp <= 5) {
      return 'Mid-Level';
    }
    return 'Senior Level';
  };

  return (
    <div>
      <div className='relative h-50'>
        <div
          className='h-full bg-cover bg-center bg-no-repeat'
          style={{ backgroundImage: 'url(/images/application-banner.png)' }}
        >
          <div className='mx-auto flex h-full max-w-200 items-center justify-between px-6 md:px-0'>
            <div>
              <h3 className='text-[40px] font-semibold text-black'>{job?.title}</h3>
              <div className='text-gray-dark mt-1 flex items-center gap-2 text-sm'>
                <p>{getJobLevel(job?.max_exp ?? 0)}</p>
                <p className='bg-gray-dark block size-0.75 rounded-full' />
                <p>{job?.location}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className='mx-auto max-w-200 px-6 md:px-0'>
        <h3 className='text-gray-dark mt-10 mb-4 text-2xl'>Role Overview</h3>
        <div className='ql-editor text-gray-dark -ml-3.5'>
          <div
            dangerouslySetInnerHTML={{
              __html: job?.description ?? '',
            }}
          />
        </div>
      </div>
    </div>
  );
};
export default JobInformation;
