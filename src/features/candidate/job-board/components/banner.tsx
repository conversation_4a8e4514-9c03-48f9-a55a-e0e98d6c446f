interface Props {
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
}

export function Banner({ searchTerm: _searchTerm, setSearchTerm: _setSearchTerm }: Props) {
  return (
    <div>
      <div className='pointer-events-none absolute inset-0 overflow-hidden'></div>

      <div className='relative overflow-hidden border-b border-slate-200/60 bg-white/80 backdrop-blur-sm'>
        <div className='to-primary-600/8 absolute inset-0 bg-gradient-to-r from-blue-600/8 via-purple-600/5' />
        <div className='relative container mx-auto w-full px-4 pt-18 pb-14 lg:max-w-7xl'>
          <div className='text-center'>
            <h1 className='mb-6 text-4xl leading-tight font-semibold tracking-tight text-black lg:text-5xl'>
              Join a Team Where
              <span className='from-primary-700 to-primary-500 relative bg-gradient-to-r bg-clip-text text-transparent'>
                {' '}
                Talent Thrives
              </span>
            </h1>
          </div>

          {/* <div className='mx-auto max-w-4xl'>
            <div className='hover:shadow-3xl rounded-3xl border border-slate-200/60 bg-white/90 p-3 shadow-2xl shadow-slate-200/60 backdrop-blur-sm transition-all duration-500 hover:shadow-slate-300/60'>
              <div className='flex gap-3'>
                <div className='relative flex-1'>
                  <Search className='absolute top-1/2 left-6 h-6 w-6 -translate-y-1/2 transform text-slate-400' />
                  <input
                    placeholder='Search by job title...'
                    className='h-16 w-full rounded-2xl border-0 bg-transparent pl-16 text-lg placeholder:text-slate-400 focus-visible:border-0 focus-visible:ring-0 focus-visible:outline-none'
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Button
                  size='lg'
                  className='group to-primary-700 from-primary-600 via-primary-500 relative h-16 overflow-hidden rounded-2xl bg-gradient-to-br px-10 font-bold text-white shadow-xl shadow-blue-600/30 transition-all duration-500'
                >
                  <div className='absolute inset-0 -translate-x-full -skew-x-12 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transition-transform duration-1000 group-hover:translate-x-full' />
                  <span className='relative z-10'>Search Jobs</span>
                </Button>
              </div>
            </div>
          </div> */}
        </div>
      </div>
    </div>
  );
}
