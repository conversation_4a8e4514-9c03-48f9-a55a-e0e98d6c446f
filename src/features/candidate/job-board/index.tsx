import { Banner } from './components/banner';
import { JobCard } from './components/job-card';
import { JobCardSkeleton } from './components/job-card-skeleton';
import { useActiveJobsQuery } from '@/hooks/api/use-job';
import { useEffect, useState } from 'react';

const JobBoard = () => {
  const [filteredJobs, setFilteredJobs] = useState<ActiveJob[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  const { data, isLoading } = useActiveJobsQuery();

  useEffect(() => {
    if (data && Array.isArray(data)) {
      const filtered = data.filter((job) =>
        job.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredJobs(filtered);
    }
  }, [data, searchTerm]);

  return (
    <main>
      <Banner searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      <div className='mx-auto my-12 max-w-4xl space-y-6 px-6 md:my-16 md:px-0'>
        <h3 className='mb-3 flex items-center gap-3 text-2xl font-medium text-black md:text-3xl'>
          {/* Latest Opportunities
       <div className='h-3 w-3 animate-pulse rounded-full bg-green-500' /> */}
          <p className='text-gray-dark'>{data?.length ?? 0} positions available</p>
        </h3>
        {isLoading ? (
          <JobCardSkeleton />
        ) : (
          filteredJobs?.map((job) => <JobCard key={job.id} job={job} />)
        )}
      </div>
    </main>
  );
};

export default JobBoard;
