import { ApiServiceInstance, QuestionsApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';

export const prepareLiveInterview = async (
  payload: LiveInterviewPayload
): Promise<IResponseData<unknown>> => {
  return QuestionsApiServiceInstance.callPostApi(API_ROUTES.PROMPT.PREPARE_LIVE_INTERVIEW, payload);
};

export const uploadPersonaPreviewCV = async (
  personaId: string,
  files: File[]
): Promise<IResponseData<{ filename: string; id: string }>> => {
  const formData = new FormData();
  formData.append('cv', files[0]);

  return ApiServiceInstance.callPostApi<{ filename: string; id: string }, FormData>(
    API_ROUTES.PROMPT.UPLOAD_PREVIEW_CV(personaId),
    formData,
    null,
    'multipart/form-data'
  );
};

export const getPersonaPreviewCVUrl = async (
  personaId: string,
  cvId: string
): Promise<IResponseData<{ presigned_url: string }>> => {
  return ApiServiceInstance.callGetApi<{ presigned_url: string }>(
    API_ROUTES.PROMPT.GET_CV_URL(personaId, cvId)
  );
};

export const getPersonaPreviewCVs = async (
  personaId: string
): Promise<IResponseData<{ id: string; filename: string }[]>> => {
  return ApiServiceInstance.callGetApi<{ id: string; filename: string }[]>(
    API_ROUTES.PROMPT.GET_PREVIEW_CVS(personaId)
  );
};
