import { ApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';
import { buildQueryString } from '@/utils/helper';

const { GET_ALL, EXPORT, GET_DETAILS, PREVIEW_CV, DELETE } = API_ROUTES.APPLICANT;

export const getApplicants = async (params: GetApplicantsParams) => {
  const queryString = buildQueryString(params);
  const url = `${GET_ALL}${queryString}`;
  return ApiServiceInstance.callGetApi<ApplicantsResponse>(url);
};

export const getApplicantDetails = async (applicantId: string) => {
  const url = GET_DETAILS(applicantId);
  return ApiServiceInstance.callGetApi<ApplicantDetails>(url);
};

export const previewApplicantCV = async (cvLink: string) => {
  return ApiServiceInstance.callGetApi<PreviewCVResponse>(PREVIEW_CV(cvLink));
};

export const deleteApplicant = async (applicantId: string) => {
  return ApiServiceInstance.callDeleteApi<void>(DELETE(applicantId));
};

export const exportApplicants = async (params: GetApplicantsParams) => {
  const queryString = buildQueryString(params);
  const url = `${EXPORT}${queryString}`;
  return ApiServiceInstance.callGetBlobApi(url);
};
