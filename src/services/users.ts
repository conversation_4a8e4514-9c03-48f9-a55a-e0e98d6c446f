import { IdentityServiceInstance } from './index';
import { API_ROUTES } from '@/config/api';
import { buildQueryString } from '@/utils/helper';

const { GET_ALL, ATTACH_ROLES, DELETE, CREATE, DETACH_ROLES } = API_ROUTES.USER;

export const getAllUsers = async (
  params?: UsersGetParams
): Promise<IResponseData<UserListResponse>> => {
  {
    const queryString = buildQueryString(params as QueryParams);
    const url = `${GET_ALL}${queryString}`;
    return IdentityServiceInstance.callGetApi<UserListResponse>(url);
  }
};

export const attachRolesToUser = async (
  payload: AttachUserRolePayload
): Promise<IResponseData<void>> => {
  return IdentityServiceInstance.callPostApi<void, AttachUserRolePayload>(ATTACH_ROLES, payload);
};

export const detachRolesToUser = async (
  payload: AttachUserRolePayload
): Promise<IResponseData<void>> => {
  return IdentityServiceInstance.callDeleteApi<AttachUserRolePayload>(
    DETACH_ROLES(payload.user_id),
    payload
  ) as unknown as Promise<IResponseData<void>>;
};

export const createUser = async (payload: UserCreatePayload): Promise<IResponseData<User>> => {
  return IdentityServiceInstance.callPostApi<User, UserCreatePayload>(CREATE, payload);
};

export const deleteUser = async (userId: string): Promise<IResponseData<void>> => {
  return IdentityServiceInstance.callDeleteApi<void>(DELETE(userId));
};
