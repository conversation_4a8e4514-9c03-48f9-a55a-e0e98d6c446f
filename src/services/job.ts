import { ApiServiceInstance, CandidateApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';
import { buildQueryString } from '@/utils/helper';

const {
  CREATE,
  DELETE,
  GET_ALL,
  GET_JOB_ROLES,
  CREATE_JOB_ROLE,
  UPDATE_JOB_ROLE,
  DELETE_JOB_ROLE,
  GET_BY_ID,
  GET_DETAILS,
  GET_ACTIVE_JOBS,
  GET_GENERATE_DESCRIPTION,
  GET_DETAILS_FOR_ADMIN,
  LIST_CANDIDATE,
} = API_ROUTES.JOB;
export const createJob = async (
  payload: JobInformation
): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callPostApi<JobInformationResponse, JobInformation>(CREATE, payload);
};

export const getGenerateJobDescription = async (jobId: string): Promise<IResponseData<string>> => {
  return ApiServiceInstance.callGetApi(GET_GENERATE_DESCRIPTION(jobId));
};

export const getJobs = async (params: GetJobsParams) => {
  const queryString = buildQueryString(params);
  const url = `${GET_ALL}${queryString}`;
  return ApiServiceInstance.callGetApi<JobListResponse>(url);
};

export const getJobById = async (id: string): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callGetApi<JobInformationResponse>(GET_BY_ID(id));
};

export const getActiveJobs = async (): Promise<IResponseData<ActiveJob[]>> => {
  return CandidateApiServiceInstance.callGetApi<ActiveJob[]>(GET_ACTIVE_JOBS);
};

export const getJob = async (id: string): Promise<IResponseData<JobInformationResponse>> => {
  return CandidateApiServiceInstance.callGetApi<JobInformationResponse>(GET_DETAILS(id));
};

export const updateJob = async (
  id: string,
  payload: Partial<JobInformation>
): Promise<IResponseData<JobInformationResponse>> => {
  return ApiServiceInstance.callPatchApi<JobInformationResponse, Partial<JobInformation>>(
    GET_BY_ID(id),
    payload
  );
};

export const getJobDetailsForAdmin = async (
  jobId: string
): Promise<IResponseData<JobDetailsAdmin>> => {
  return ApiServiceInstance.callGetApi<JobDetailsAdmin>(GET_DETAILS_FOR_ADMIN(jobId));
};

export const getJobCandidates = async (
  jobId: string,
  params: GetJobsParams
): Promise<IResponseData<JobCandidateResponse>> => {
  return ApiServiceInstance.callGetApi<JobCandidateResponse>(
    LIST_CANDIDATE(jobId),
    null,
    params as QueryParams
  );
};
export const deleteJob = async (id: string): Promise<IResponseData<void>> => {
  return ApiServiceInstance.callDeleteApi<void>(DELETE(id));
};

// Job Role APIs
export const getJobRoles = async (
  params?: GetJobsParams
): Promise<IResponseData<PaginatedList<JobRole>>> => {
  const queryString = buildQueryString(params || {});
  const url = `${GET_JOB_ROLES}${queryString}`;
  return ApiServiceInstance.callGetApi<PaginatedList<JobRole>>(url);
};

export const createJobRole = async (payload: JobRolePayload): Promise<IResponseData<JobRole>> => {
  return ApiServiceInstance.callPostApi<JobRole, JobRolePayload>(CREATE_JOB_ROLE, payload);
};

export const updateJobRole = async (
  roleId: string,
  payload: JobRolePayload
): Promise<IResponseData<JobRole>> => {
  return ApiServiceInstance.callPatchApi<JobRole, JobRolePayload>(UPDATE_JOB_ROLE(roleId), payload);
};

export const deleteJobRole = async (roleId: string): Promise<IResponseData<void>> => {
  return ApiServiceInstance.callDeleteApi<void>(DELETE_JOB_ROLE(roleId));
};
