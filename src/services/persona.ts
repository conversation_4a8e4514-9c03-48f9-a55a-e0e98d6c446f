import { ApiServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';

const {
  GET_ALL,
  ATTACH_TO_JOB,
  GET_JOB_PERSONAS,
  GET_BY_ID,
  DELETE,
  GENERATE_PROMPT,
  GET_JOBS,
  CREATE,
  UPDATE,
} = API_ROUTES.PERSONA;

export const getPersonas = async (
  params?: ListingParams
): Promise<IResponseData<PaginatedList<ExtendedPersona>>> => {
  return ApiServiceInstance.callGetApi<PaginatedList<ExtendedPersona>>(
    GET_ALL,
    null,
    params as QueryParams
  );
};

export const attachPersonaToJob = async (payload: {
  jobId: string;
  list: { personas: JobPersona[] };
}): Promise<IResponseData<unknown>> => {
  return ApiServiceInstance.callPostApi<unknown, { personas: JobPersona[] }>(
    ATTACH_TO_JOB(payload.jobId),
    payload.list
  );
};

export const getJobPersonas = async (jobId: string): Promise<IResponseData<AttachJobPersona[]>> => {
  return ApiServiceInstance.callGetApi<AttachJobPersona[]>(GET_JOB_PERSONAS(jobId));
};

export const getPersonaById = async (
  personaId: string
): Promise<IResponseData<ExtendedPersona>> => {
  return ApiServiceInstance.callGetApi<ExtendedPersona>(GET_BY_ID(personaId));
};

export const generatePrompt = async (
  payload: PromptGenerationPayload
): Promise<IResponseData<PromptGenerateResponse>> => {
  return ApiServiceInstance.callPostApi<PromptGenerateResponse, PromptGenerationPayload>(
    GENERATE_PROMPT,
    payload
  );
};

export const deletePersona = async (personaId: string): Promise<IResponseData<void>> => {
  return ApiServiceInstance.callDeleteApi<void>(DELETE(personaId));
};

export const getJobsByPersona = async (
  personaId: string,
  params: ListingParams
): Promise<IResponseData<PaginatedList<PersonaAttachedJobs>>> => {
  return ApiServiceInstance.callGetApi<PaginatedList<PersonaAttachedJobs>>(
    GET_JOBS(personaId),
    null,
    params as QueryParams
  );
};

export const createPersona = async (
  payload: PersonaCreatePayload
): Promise<IResponseData<ExtendedPersona>> => {
  return ApiServiceInstance.callPostApi<ExtendedPersona, PersonaCreatePayload>(CREATE, payload);
};

export const updatePersona = async (
  personaId: string,
  payload: Partial<BasePersona>
): Promise<IResponseData<ExtendedPersona>> => {
  return ApiServiceInstance.callPatchApi<ExtendedPersona, Partial<BasePersona>>(
    UPDATE(personaId),
    payload
  );
};
