import { IdentityServiceInstance } from '.';
import { API_ROUTES } from '@/config/api';
import { buildQueryString } from '@/utils/helper';

const { GET_ALL, CREATE, UPDATE, DELETE } = API_ROUTES.ROLE;
export const getAllRoles = async (params?: RolesParams): Promise<IResponseData<RoleResponse>> => {
  const queryString = buildQueryString(params || {});
  const url = `${GET_ALL}${queryString}`;
  return IdentityServiceInstance.callGetApi<RoleResponse>(url);
};

export const createRole = async (payload: RoleSavePayload): Promise<IResponseData<Role>> => {
  return IdentityServiceInstance.callPostApi<Role, RoleSavePayload>(CREATE, payload);
};

export const updateRole = async (
  roleId: number,
  payload: RoleSavePayload
): Promise<IResponseData<Role>> => {
  return IdentityServiceInstance.callPutApi<Role, RoleSavePayload>(UPDATE(roleId), payload);
};

export const deleteRole = async (roleId: number): Promise<IResponseData<Role>> => {
  return IdentityServiceInstance.callDeleteApi<Role>(DELETE(roleId));
};
