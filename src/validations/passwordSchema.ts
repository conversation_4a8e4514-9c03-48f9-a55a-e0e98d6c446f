import { z } from 'zod';

export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(4, {
      message: 'Current password must be at least 4 characters.',
    }),
    newPassword: z
      .string()
      .min(4, {
        message: 'New password must be at least 4 characters.',
      })
      .max(20, {
        message: 'New password must not be longer than 20 characters.',
      }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

export type ChangePasswordValues = z.infer<typeof changePasswordSchema>;
