import { z } from 'zod';

const basePersonaSchema = z.object({
  title: z.string().min(1, 'Title is required').max(55, 'Title is too long'),
  role: z.string().min(1, 'Role is required'),
  difficulty: z.string().min(1, 'Difficulty is required'),
  keywords: z.array(z.string()).min(1, 'At least one keyword is required'),
  expectations: z.string().min(1, 'At least one expectation is required'),
});

// Schema with optional description and prompt_text, used for the base form type
const guidedModeOptionalFieldsSchema = basePersonaSchema.extend({
  description: z.string().optional(),
  prompt_text: z.string().optional(),
});

// Schema with required description and prompt_text, used when AI generation is active
const guidedModeRequiredFieldsSchema = guidedModeOptionalFieldsSchema.extend({
  description: z.string().min(1, 'Description is required'),
  prompt_text: z.string().min(1, 'Prompt is required'),
});

export const getPersonaSchema = (showAiGeneration: boolean) => {
  if (showAiGeneration) {
    return guidedModeRequiredFieldsSchema;
  }
  return guidedModeOptionalFieldsSchema;
};

export const guidedModePersonaSchema = guidedModeOptionalFieldsSchema;
export type GuidedModePersonaType = z.infer<typeof guidedModePersonaSchema>;

export const manualModePersonaSchema = z.object({
  title: z.string().min(1, 'Title is required').max(55, 'Title is too long'),
  description: z.string().min(1, 'Description is required'),
  prompt_text: z.string().min(1, 'Prompt is required'),
});

export type ManualModePersonaType = z.infer<typeof manualModePersonaSchema>;

export const previewPersonaSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  prompt_text: z.string().min(1, 'Prompt is required'),
});

export type PreviewPersonaType = z.infer<typeof previewPersonaSchema>;
