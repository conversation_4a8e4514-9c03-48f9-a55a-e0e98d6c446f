import * as z from 'zod';

export const createUserSchema = z
  .object({
    user_name: z.string().min(1, 'User name is required'),
    email: z.string().email('Invalid email address'),
    password: z.string().min(8, 'Password must be at least 8 characters long'),
    confirm_password: z.string().min(8, 'Confirm Password must be at least 8 characters long'),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: 'Passwords must match',
    path: ['confirm_password'],
  });

export type CreateUserSchema = z.infer<typeof createUserSchema>;
