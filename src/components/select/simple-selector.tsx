import { Label } from '../ui';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/libs/utils';
import { forwardRef, useEffect, useState } from 'react';

interface Props {
  options: SelectProp[] | [];
  label?: string;
  error?: string;
  placeholder?: string;
  className?: string;
  name?: string;
  defaultValue?: string;
  onChange?: (val: string | number) => void;
  value?: string;
}

const SimpleSelect = forwardRef<HTMLDivElement, Props>(
  ({ options, className, name, label, error, placeholder, value, defaultValue, onChange }, ref) => {
    const [selectedValue, setSelectedValue] = useState<string | undefined>(value || defaultValue);

    useEffect(() => {
      if (value !== undefined && value !== selectedValue && value != '') {
        setSelectedValue(value);
      }
    }, [value]);

    const handleChange = (val: string) => {
      setSelectedValue(val);
      if (onChange) {
        onChange(val);
      }
    };

    return (
      <div>
        {label && (
          <Label htmlFor={label} className='mb-3'>
            {label}
          </Label>
        )}
        <Select value={selectedValue} onValueChange={handleChange} name={name}>
          <SelectTrigger
            className={cn(
              'w-full',
              {
                'border-destructive focus-visible:border-input focus-visible:ring-destructive':
                  error,
                'text-muted-foreground': !value,
              },
              className
            )}
          >
            <SelectValue placeholder={placeholder ?? 'Select'} />
          </SelectTrigger>
          <SelectContent ref={ref} className='z-[100]'>
            {options.length > 0 ? (
              options.map((option: SelectProp) => (
                <SelectItem value={option.value} key={option.value}>
                  {option.label}
                </SelectItem>
              ))
            ) : (
              <p className='text-gray-light py-4 text-center text-sm'>No Options Available</p>
            )}
          </SelectContent>
        </Select>
      </div>
    );
  }
);

SimpleSelect.displayName = 'SimpleSelect';

export default SimpleSelect;
