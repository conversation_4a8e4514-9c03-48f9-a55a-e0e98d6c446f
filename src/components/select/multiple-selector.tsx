import { useClickOutside } from '@/hooks/use-click-outside';
import { cn } from '@/libs/utils';
import { ChevronDown, X, Search } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';

const MAX_HEIGHT = 200;
const DROPDOWN_PADDING = 16;

interface Option {
  value: string;
  label: string;
}

interface MultiSelectProps {
  options: Option[];
  value?: Option[];
  onChange?: (selectedOptions: Option[]) => void;
  placeholder?: string;
  error?: string;
}

export function MultiSelect({
  options = [],
  value = [],
  onChange,
  placeholder = 'Select options...',
  error,
}: MultiSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<'bottom' | 'top'>('bottom');
  const [maxDropdownHeight, setMaxDropdownHeight] = useState(MAX_HEIGHT);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const updateDropdownPosition = () => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom - DROPDOWN_PADDING;
      const spaceAbove = rect.top - DROPDOWN_PADDING;

      // Calculate the height needed for search input + border (approximately 60px)
      const searchSectionHeight = 60;

      if (spaceBelow < MAX_HEIGHT && spaceAbove > spaceBelow) {
        setDropdownPosition('top');
        // Set max height based on available space above
        setMaxDropdownHeight(Math.min(MAX_HEIGHT, spaceAbove - searchSectionHeight));
      } else {
        setDropdownPosition('bottom');
        // Set max height based on available space below
        setMaxDropdownHeight(Math.min(MAX_HEIGHT, spaceBelow - searchSectionHeight));
      }
    }
  };

  useClickOutside(dropdownRef as React.RefObject<HTMLElement>, () => setIsOpen(false));

  useEffect(() => {
    const handleScroll = () => {
      if (isOpen) {
        updateDropdownPosition();
      }
    };

    const handleResize = () => {
      if (isOpen) {
        updateDropdownPosition();
      }
    };

    window.addEventListener('scroll', handleScroll, true); // Use capture phase
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const toggleDropdown = () => {
    if (!isOpen) {
      updateDropdownPosition();
    }
    setIsOpen(!isOpen);
    setSearchTerm('');
  };

  const toggleOption = (option: Option) => {
    const updatedSelection = value.some((item) => item.value === option.value)
      ? value.filter((item) => item.value !== option.value)
      : [...value, option];

    onChange?.(updatedSelection);
  };

  const removeOption = (option: Option) => {
    const updatedSelection = value.filter((item) => item.value !== option.value);
    onChange?.(updatedSelection);
  };

  const availableOptions = options.filter(
    (option) => !value.some((selected) => selected.value === option.value)
  );

  const filteredOptions = availableOptions.filter((option) =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='relative w-full' ref={dropdownRef}>
      <div
        ref={triggerRef}
        className={cn(
          'border-input flex h-12 cursor-pointer flex-wrap items-center overflow-y-auto rounded-md border px-3',
          {
            'border-destructive focus-visible:border-input focus-visible:ring-destructive': error,
          }
        )}
        onClick={toggleDropdown}
      >
        {value.length === 0 ? (
          <span className='text-muted-foreground text-sm'>{placeholder}</span>
        ) : (
          value.map((option) => (
            <span
              key={option.value}
              className='bg-primary-50 text-primary-500 dark:text-primary mr-2 flex items-center gap-1 rounded px-2.5 py-0.5 text-sm font-medium'
            >
              {option.label}
              <button
                type='button'
                onClick={(e) => {
                  e.stopPropagation();
                  removeOption(option);
                }}
                className='text-primary-600 ml-1'
                aria-label={`Remove ${option.label}`}
              >
                <X size={14} />
              </button>
            </span>
          ))
        )}
        <ChevronDown className='ml-auto size-4 opacity-50' />
      </div>
      {isOpen && (
        <div
          className={`bg-card absolute z-50 w-full rounded-md border border-gray-300 shadow-lg ${
            dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'
          }`}
          style={{
            maxHeight: maxDropdownHeight + 60,
          }}
        >
          <div className='border-b border-gray-300 p-2'>
            <div className='relative'>
              <input
                ref={searchInputRef}
                type='text'
                className='border-input focus:ring-ring h-10 w-full rounded-md border py-2 pr-4 pl-8 text-sm focus:ring-1 focus:outline-none'
                placeholder='Search options...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Search
                className='absolute top-1/2 left-2 -translate-y-1/2 text-gray-400'
                size={18}
              />
            </div>
          </div>
          {filteredOptions.length > 0 ? (
            <ul
              className='overflow-auto'
              style={{
                maxHeight: maxDropdownHeight,
              }}
            >
              {filteredOptions.map((option) => (
                <li
                  key={option.value}
                  className='dark:text-foreground cursor-pointer px-6 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800'
                  onClick={() => toggleOption(option)}
                >
                  {option.label}
                </li>
              ))}
            </ul>
          ) : (
            <div className='px-4 py-2 text-gray-500'>No options found</div>
          )}
        </div>
      )}
    </div>
  );
}
