import { Avatar, AvatarFallback } from '../ui';
import { Bo<PERSON> } from 'lucide-react';

export const TypingIndicator = () => (
  <div className='flex items-start justify-start gap-3'>
    <Avatar className='bg-primary-100 h-10 w-10 flex-shrink-0'>
      <AvatarFallback className='bg-primary-100'>
        <Bot className='text-primary-600 h-5 w-5' />
      </AvatarFallback>
    </Avatar>
    <div className='bg-card text-card-foreground rounded-2xl border px-4 py-3'>
      <div className='flex items-center gap-1'>
        <div className='flex gap-1'>
          <div className='h-2 w-2 animate-bounce rounded-full bg-gray-400 [animation-delay:-0.3s]'></div>
          <div className='h-2 w-2 animate-bounce rounded-full bg-gray-400 [animation-delay:-0.15s]'></div>
          <div className='h-2 w-2 animate-bounce rounded-full bg-gray-400'></div>
        </div>
        <span className='text-muted-foreground ml-2 text-sm'>AI is typing...</span>
      </div>
    </div>
  </div>
);
