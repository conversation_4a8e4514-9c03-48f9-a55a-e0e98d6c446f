'use client';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { cn } from '@/libs/utils';
import { AlertCircle, Loader2 } from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'sonner';

/* eslint-disable @typescript-eslint/no-explicit-any */

interface DeleteConfirmationModalInputProps {
  children?: React.ReactNode;
  title?: string;
  description?: string;
  onConfirm: () => Promise<any>;
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  confirmationText?: string;
  confirmationPlaceholder?: string;
  showWarning?: boolean;
  warningTitle?: string;
  warningDescription?: string;
  iconClassName?: string;
  iconWrapperClassName?: string;
  confirmButtonClassName?: string;
}

export function ConfirmationModalInput({
  title = 'Delete Confirmation',
  description = 'Are you sure you want to delete this item? This action cannot be undone.',
  onConfirm,
  children,
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange,
  confirmationText = 'DELETE',
  confirmationPlaceholder = 'Enter DELETE to confirm deletion',
  showWarning = false,
  warningTitle = 'Warning!',
  warningDescription = 'Please be careful, this action can not be rolled back.',
  iconClassName = 'size-6 text-red-600 dark:text-red-400',
  iconWrapperClassName = 'bg-red-100 dark:bg-red-900/20',
  confirmButtonClassName = 'bg-red-700',
}: DeleteConfirmationModalInputProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : internalOpen;
  const setOpen = isControlled ? controlledOnOpenChange || (() => {}) : setInternalOpen;

  const handleDelete = async () => {
    if (inputValue.trim() !== confirmationText) return;

    setIsDeleting(true);
    try {
      await onConfirm()
        .then()
        .catch((err: any) => console.log('Failed to delete item: ', JSON.stringify(err)));
      setOpen(false);
      setInputValue('');
    } catch {
      toast.error('Failed to delete item');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent className='sm:max-w-md md:max-w-lg'>
        <DialogHeader>
          <div className='flex items-center gap-4'>
            <div
              className={cn(
                'flex h-12 w-12 items-center justify-center rounded-full',
                iconWrapperClassName
              )}
            >
              <AlertCircle className={iconClassName} />
            </div>
            <DialogTitle>{title}</DialogTitle>
          </div>
          <DialogDescription className='text-gray-dark max-w-sm pt-4 pb-2 text-center md:max-w-lg'>
            {description}
          </DialogDescription>
        </DialogHeader>
        <div className='space-y-4'>
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder={confirmationPlaceholder}
            className='mt-1'
          />

          {showWarning && (
            <Alert variant='destructive'>
              <AlertTitle>{warningTitle}</AlertTitle>
              <AlertDescription>{warningDescription}</AlertDescription>
            </Alert>
          )}
        </div>
        <DialogFooter className='mt-2 grid grid-cols-2 gap-2'>
          <Button
            variant='outline'
            onClick={() => {
              setOpen(false);
              setInputValue('');
            }}
            className='h-10 w-full sm:w-auto'
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant='destructive'
            onClick={handleDelete}
            className={cn(
              'group relative h-10 w-full overflow-hidden bg-rose-600 sm:w-auto',
              confirmButtonClassName
            )}
            disabled={isDeleting || inputValue.trim() !== confirmationText}
          >
            {isDeleting ? (
              <>
                <span className='opacity-0'>Delete</span>
                <span className='absolute inset-0 flex items-center justify-center'>
                  <Loader2 className='h-4 w-4 animate-spin' />
                </span>
              </>
            ) : (
              <>
                <span className='relative z-10 flex items-center gap-1'>Confirm</span>
                <span
                  className={cn(`absolute inset-0 translate-y-full`, confirmButtonClassName)}
                ></span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
