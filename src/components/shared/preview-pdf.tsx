import PdfViewerContent from '@/components/shared/pdf-viewer-content';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { ExternalLink, Maximize, Minus, Plus, RefreshCcw, RotateCcw } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface PreviewPdfProps {
  fileUrl?: string;
  isLoading?: boolean;
  isPreviewDataLoading?: boolean;
  scale?: number;
  className?: string;
  styles?: React.CSSProperties;
}

const PreviewPdf = ({
  fileUrl,
  isLoading = false,
  isPreviewDataLoading = false,
  scale: initialScale = 1,
  className = '',
  styles,
}: PreviewPdfProps) => {
  const [scale, setScale] = useState<number>(initialScale);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  useEffect(() => {
    if (isModalOpen) {
      setTimeout(() => {
        setScale(1);
      }, 100);
    }
  }, [isModalOpen]);

  const containerRef = useRef<HTMLDivElement | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [startX, setStartX] = useState<number>(0);
  const [startY, setStartY] = useState<number>(0);
  const [scrollLeft, setScrollLeft] = useState<number>(0);
  const [scrollTop, setScrollTop] = useState<number>(0);

  const zoomIn = () => setScale((prev) => Math.min(prev + 0.2, 2)); // Max 2x
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.2, 0.5)); // Min 0.5x
  const resetZoom = () => setScale(0.75); // Reset to default 0.75

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!containerRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - containerRef.current.offsetLeft);
    setStartY(e.pageY - containerRef.current.offsetTop);
    setScrollLeft(containerRef.current.scrollLeft);
    setScrollTop(containerRef.current.scrollTop);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current) return;
    const x = e.pageX - containerRef.current.offsetLeft;
    const y = e.pageY - containerRef.current.offsetTop;
    containerRef.current.scrollLeft = scrollLeft - (x - startX);
    containerRef.current.scrollTop = scrollTop - (y - startY);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  return (
    <>
      <div className={`flex flex-col overflow-y-auto ${className}`} style={styles}>
        <div className='relative h-[calc(100vh-100px)] flex-1 overflow-hidden rounded-lg border border-gray-200'>
          {fileUrl && !isLoading && (
            <div className='absolute top-4 right-4 mb-4 flex flex-col justify-end gap-2'>
              <Button
                variant='outline'
                size='lg'
                className={`size-10 ${scale >= 2 ? 'cursor-not-allowed opacity-50' : 'cursor-zoom-in'}`}
                onClick={zoomIn}
                disabled={scale >= 2}
                aria-label='Zoom in'
              >
                <Plus className='size-4' />
              </Button>
              <Button
                variant='outline'
                size='lg'
                className={`size-10 ${scale <= 0.5 ? 'cursor-not-allowed opacity-50' : 'cursor-zoom-out'}`}
                onClick={zoomOut}
                disabled={scale <= 0.5}
                aria-label='Zoom out'
              >
                <Minus className='size-4' />
              </Button>
              <Button
                variant='outline'
                size='lg'
                className='size-10 cursor-pointer'
                onClick={() => window.open(fileUrl, '_blank')}
                aria-label='Open in new tab'
              >
                <ExternalLink className='size-4' />
              </Button>
              <Button
                variant='outline'
                size='lg'
                className='size-10 cursor-pointer'
                onClick={() => setIsModalOpen(true)}
                aria-label='View in larger mode'
              >
                <Maximize className='size-4' />
              </Button>
              <Button
                variant='outline'
                size='lg'
                className='size-10 cursor-pointer'
                onClick={resetZoom}
                aria-label='Reset zoom'
              >
                <RefreshCcw className='size-4' />
              </Button>
            </div>
          )}
          <PdfViewerContent
            fileUrl={fileUrl}
            isLoading={isLoading}
            isPreviewDataLoading={isPreviewDataLoading}
            scale={scale}
            containerRef={containerRef}
            handleMouseDown={handleMouseDown}
            handleMouseMove={handleMouseMove}
            handleMouseUp={handleMouseUp}
          />
        </div>
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent
          className='!h-full !max-w-full !rounded-none !p-0'
          closeButtonClassName='border border-gray-300 size-8 text-black flex items-center justify-center rounded-md shadow'
        >
          <div className='relative size-full'>
            {fileUrl && !isLoading && (
              <div className='absolute top-1/2 right-4 z-50 mb-4 flex -translate-y-1/2 flex-col justify-end gap-2'>
                <Button
                  variant='outline'
                  size='lg'
                  className={`size-10 ${scale >= 2 ? 'cursor-not-allowed opacity-50' : 'cursor-zoom-in'}`}
                  onClick={zoomIn}
                  disabled={scale >= 2}
                  aria-label='Zoom in'
                >
                  <Plus className='size-4' />
                </Button>
                <Button
                  variant='outline'
                  size='lg'
                  className={`size-10 ${scale <= 0.5 ? 'cursor-not-allowed opacity-50' : 'cursor-zoom-out'}`}
                  onClick={zoomOut}
                  disabled={scale <= 0.5}
                  aria-label='Zoom out'
                >
                  <Minus className='size-4' />
                </Button>
                <Button
                  variant='outline'
                  size='lg'
                  className='size-10 cursor-pointer'
                  onClick={() => window.open(fileUrl, '_blank')}
                  aria-label='Open in new tab'
                >
                  <ExternalLink className='size-4' />
                </Button>
                <Button
                  variant='outline'
                  size='lg'
                  className='size-10 cursor-pointer'
                  onClick={resetZoom}
                  aria-label='Reset zoom'
                >
                  <RotateCcw className='size-4' />
                </Button>
              </div>
            )}
            <PdfViewerContent
              fileUrl={fileUrl}
              isLoading={isLoading}
              isPreviewDataLoading={isPreviewDataLoading}
              scale={scale}
              containerRef={containerRef}
              handleMouseDown={handleMouseDown}
              handleMouseMove={handleMouseMove}
              handleMouseUp={handleMouseUp}
              className='!h-[calc(100vh-10px)]'
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PreviewPdf;
