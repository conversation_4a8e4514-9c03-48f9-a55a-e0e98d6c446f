import { Button } from '@/components/ui/button';
import { Pagination, PaginationContent, PaginationItem } from '@/components/ui/pagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { usePagination } from '@/hooks/use-pagination';
import { DEFAULT_PAGE_SIZE } from '@/utils/constants';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface ITablePagination {
  totalRecords: number;
}

export default function TablePagination({ totalRecords }: ITablePagination) {
  const {
    pageSize,
    currentPageNumber,
    isFirstPage,
    isLastPage,
    goToPage,
    goToPreviousPage,
    goToNextPage,
    changePageSize,
    pageCount,
  } = usePagination({
    totalItems: totalRecords,
  });

  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;

    if (pageCount <= maxPagesToShow) {
      for (let i = 1; i <= pageCount; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);

      // Calculate start and end of middle pages
      let startPage = Math.max(2, currentPageNumber - 1);
      let endPage = Math.min(pageCount - 1, currentPageNumber + 1);

      // Adjust if we're near the beginning
      if (currentPageNumber <= 3) {
        endPage = 4;
      }

      // Adjust if we're near the end
      if (currentPageNumber >= pageCount - 2) {
        startPage = pageCount - 3;
      }

      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pages.push(-1); // -1 represents ellipsis
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis before last page if needed
      if (endPage < pageCount - 1) {
        pages.push(-2); // -2 represents ellipsis
      }

      pages.push(pageCount);
    }

    return pages;
  };

  if (totalRecords === 0) return null;

  return (
    <div className='flex flex-1 justify-between gap-4'>
      {/* Left side: Page size selector */}
      <div className='flex items-center gap-2 text-sm'>
        <Select
          onValueChange={(size) => changePageSize(parseInt(size))}
          value={pageSize.toString() ?? DEFAULT_PAGE_SIZE}
        >
          <SelectTrigger className='!h-10 w-[75px]'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {/* TODO: Fix the logic for selecting the correct page size as it gets override in params */}
            {[10].map((size) => (
              <SelectItem key={size} value={size.toString()}>
                {size}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        per page
      </div>

      {/* Right side: Pagination */}
      <Pagination className='w-fit'>
        <PaginationContent>
          <PaginationItem>
            <Button variant={'ghost'} onClick={goToPreviousPage} disabled={isFirstPage}>
              <ChevronLeft className='mr-1.5 size-3.5' />
              Previous
            </Button>
          </PaginationItem>
          {getPageNumbers().map((page, index) =>
            page < 0 ? (
              <span key={`ellipsis-${index}`} className='px-2'>
                &hellip;
              </span>
            ) : (
              <Button
                key={page}
                variant={currentPageNumber === page ? 'default' : 'outline'}
                size='icon'
                className='mx-0.5 h-8 w-8'
                onClick={() => goToPage(page)}
                aria-label={`Page ${page}`}
                aria-current={currentPageNumber === page ? 'page' : undefined}
              >
                {page}
              </Button>
            )
          )}
          {/* <PaginationItem>
                <div className="px-2 text-sm text-muted-foreground">
                  Page {currentPageNumber + 1} of {pageCount(totalRecords)}
                </div>
              </PaginationItem> */}
          <PaginationItem>
            <Button variant={'ghost'} onClick={goToNextPage} disabled={isLastPage}>
              Next
              <ChevronRight className='ml-1.5 size-3.5' />
            </Button>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
