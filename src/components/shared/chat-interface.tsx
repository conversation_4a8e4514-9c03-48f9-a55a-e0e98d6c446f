import { ScrollArea } from '@/components/ui/scroll-area';
import { Bot } from 'lucide-react';
import { useRef } from 'react';

interface QAItem {
  question: string;
  answer: string;
}

interface ChatInterfaceProps {
  qa_array: QAItem[];
  className?: string;
  height?: string;
  renderEmptyState?: React.ReactNode;
  candidateAvatar?: React.ReactNode;
  interviewerAvatar?: React.ReactNode;
}

interface MessageProp {
  sender: 'candidate' | 'interviewer';
  content: string;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  qa_array,
  className = '',
  height = 'h-full',
  renderEmptyState = null,
  candidateAvatar,
  interviewerAvatar,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Convert qa_array to messages format
  const messages: MessageProp[] = qa_array.flatMap((qa) => [
    { sender: 'interviewer' as const, content: qa.question },
    { sender: 'candidate' as const, content: qa.answer },
  ]);

  const renderMessages = () => {
    return (
      <ScrollArea className='flex-1 rounded-xl p-6'>
        <div className='mx-auto max-w-3xl space-y-6'>
          {messages.map((message, index) => (
            <div
              key={index}
              className={`flex items-start gap-3 ${message.sender === 'candidate' ? 'justify-end' : 'justify-start'}`}
            >
              {message.sender === 'interviewer' && interviewerAvatar && (
                <div className='flex-shrink-0'>
                  {interviewerAvatar}
                </div>
              )}
              <div
                className={`max-w-[70%] rounded-lg p-4 break-words ${
                  message.sender === 'candidate'
                    ? 'bg-primary text-primary-foreground'
                    : 'border border-gray-200 bg-white shadow-sm'
                }`}
              >
                <p className='text-sm whitespace-pre-wrap'>{message.content}</p>
              </div>
              {message.sender === 'candidate' && candidateAvatar && (
                <div className='flex-shrink-0'>
                  {candidateAvatar}
                </div>
              )}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
    );
  };

  const renderDefaultEmptyState = () => {
    if (renderEmptyState) return renderEmptyState;
    return (
      <div className='flex flex-1 flex-col items-center justify-center'>
        <div className='flex flex-col items-center justify-center gap-2'>
          <div className='rounded-full bg-gray-100 p-3'>
            <Bot className='size-8 text-gray-500' />
          </div>
          <p className='text-center text-sm text-gray-500'>
            The candidate might not attend the interview yet
          </p>
        </div>
      </div>
    );
  };

  return (
    <div className={`flex ${height} flex-col ${className}`}>
      {messages.length > 0 ? renderMessages() : renderDefaultEmptyState()}
    </div>
  );
};

export default ChatInterface;
