'use client';

import { sidebarData } from '@/components/layout/data/sidebar-data';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/libs/utils';
import { NavItem } from '@/types/sidebar';
import { Link } from '@tanstack/react-router';
import { useLocation } from '@tanstack/react-router';
import { motion } from 'framer-motion';

const getNavigationItemClasses = (isActive: boolean, isCollapsed: boolean) => {
  return cn(
    'flex items-center rounded-lg py-2 mb-1 text-sm font-normal transition-colors',
    isActive
      ? 'bg-accent text-accent-foreground'
      : 'hover:bg-accent/50 text-muted-foreground hover:text-foreground',
    isCollapsed ? 'pl-4' : 'px-3'
  );
};

interface NavigationItemProps {
  item: NavItem;
  isCollapsed: boolean;
  pathname: string;
}

function NavigationItem({ item, isCollapsed, pathname }: NavigationItemProps) {
  // Handle items with sub-items (collapsible groups)
  if (item.items && item.items.length > 0) {
    // For collapsed state, show parent item only
    if (isCollapsed) {
      return (
        <div
          key={item.title}
          className={getNavigationItemClasses(false, isCollapsed)}
          title={item.title}
        >
          {item.icon && <item.icon className='size-5 shrink-0' />}
        </div>
      );
    }

    // For expanded state, show collapsible group
    return (
      <div key={item.title} className='space-y-1'>
        <div className={getNavigationItemClasses(false, isCollapsed)}>
          {item.icon && <item.icon className='size-5 shrink-0' />}
          <motion.span
            initial={false}
            animate={{
              opacity: isCollapsed ? 0 : 1,
              width: isCollapsed ? 0 : 'auto',
            }}
            transition={{ duration: 0.2 }}
            className='ml-3 overflow-hidden whitespace-nowrap'
          >
            {item.title}
          </motion.span>
        </div>
        {/* Sub-items */}
        <div className='ml-6 space-y-1'>
          {item.items.map((subItem) => {
            const isActive = pathname === subItem.url;
            return (
              <Link key={subItem.url} to={subItem.url}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={getNavigationItemClasses(isActive, false)}
                >
                  {subItem.icon && (
                    <subItem.icon
                      className='size-4 shrink-0'
                      variant={isActive ? 'active' : 'default'}
                    />
                  )}
                  <span className='ml-2 text-sm'>{subItem.title}</span>
                </motion.div>
              </Link>
            );
          })}
        </div>
      </div>
    );
  }

  // Handle regular navigation items
  const isActive = pathname.includes(item.url ?? '');
  return (
    <Link key={item.url} to={item.url}>
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={getNavigationItemClasses(isActive, isCollapsed)}
        title={isCollapsed ? item.title : undefined}
      >
        {item.icon && (
          <item.icon className='size-5 shrink-0' variant={isActive ? 'active' : 'default'} />
        )}

        {!isCollapsed && (
          <motion.div
            initial={false}
            animate={{
              opacity: isCollapsed ? 0 : 1,
              width: isCollapsed ? 0 : 'auto',
            }}
            transition={{ duration: 0.2 }}
            className='flex flex-1 items-center justify-between overflow-hidden whitespace-nowrap'
          >
            <span className='ml-3'>{item.title}</span>
            {item.badge && (
              <Badge
                variant='secondary'
                className='ml-2 flex size-5 items-center justify-center p-0 text-xs'
              >
                {item.badge}
              </Badge>
            )}
          </motion.div>
        )}
      </motion.div>
    </Link>
  );
}

interface SidebarNavigationProps {
  isCollapsed: boolean;
}

export function SidebarNavigation({ isCollapsed }: SidebarNavigationProps) {
  const location = useLocation();
  const pathname = location.pathname;

  return (
    <div className='flex-1 overflow-y-auto py-4'>
      {sidebarData.navGroups.map((group) => (
        <div key={group.title} className='mb-6'>
          {/* Group Title - only show when expanded */}
          {!isCollapsed && (
            <motion.div
              initial={false}
              animate={{
                opacity: isCollapsed ? 0 : 1,
              }}
              transition={{ duration: 0.2 }}
              className='px-6 py-1'
            >
              <div className='text-muted-foreground text-xs font-medium tracking-wider'>
                {group.title}
              </div>
            </motion.div>
          )}

          <nav className='mt-2 space-y-1 px-3'>
            {group.items.map((item) => (
              <NavigationItem
                key={item.title}
                item={item}
                isCollapsed={isCollapsed}
                pathname={pathname}
              />
            ))}
          </nav>
        </div>
      ))}
    </div>
  );
}
