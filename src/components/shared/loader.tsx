import { cn } from '@/libs/utils';
import React from 'react';

type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

const SIZE_MAP: Record<Size, string> = {
  xs: 'w-8 h-8',
  sm: 'w-12 h-12',
  md: 'w-16 h-16',
  lg: 'w-24 h-24',
  xl: 'w-32 h-32',
};

interface Props {
  size?: Size;
  className?: string;
  type?: 'page' | 'inline';
}

export const Loader: React.FC<Props> = ({ size = 'md', className, type = 'inline' }) => (
  <div
    className={cn(
      {
        'flex h-screen items-center justify-center': type === 'page',
      },
      className
    )}
  >
    <div className={`relative ${SIZE_MAP[size]}`}>
      <div className='absolute inset-0 rounded-full shadow-inner' />
      <div className='absolute inset-0 animate-spin rounded-full shadow-[0_4px_0_#5c92fa_inset]' />
    </div>
  </div>
);
