'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Link } from '@tanstack/react-router';
import { Search } from 'lucide-react';

interface Props {
  title?: string;
  description?: string;
  buttonLabel?: string;
  buttonLink?: string;
}

export function NotFoundList({
  title = 'Not Found',
  description = 'Sorry, the item you are looking for no longer available or does not exist.',
  buttonLabel = 'Back to Home',
  buttonLink = '/admin/dashboard',
}: Props) {
  return (
    <div className='flex min-h-screen items-center justify-center p-4'>
      <div className='w-full max-w-md text-center'>
        <div className='mb-6 flex justify-center'>
          <div className='flex h-16 w-16 items-center justify-center rounded-full bg-gray-100'>
            <Search className='h-8 w-8 text-gray-400' />
          </div>
        </div>

        <h1 className='text-gray-dark mb-3 text-2xl font-semibold'>{title}</h1>

        <p className='text-gray-light mb-8'>{description}</p>

        <Link to={buttonLink}>
          <Button className='w-full'>{buttonLabel}</Button>
        </Link>
      </div>
    </div>
  );
}
