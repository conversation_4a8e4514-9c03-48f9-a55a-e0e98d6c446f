/* eslint-disable @typescript-eslint/no-explicit-any */
import type * as pdfjsLib from 'pdfjs-dist';
import { useState, useEffect, useRef, Fragment } from 'react';
import { toast } from 'sonner';

interface PdfViewerContentProps {
  fileUrl?: string;
  isLoading?: boolean;
  isPreviewDataLoading?: boolean;
  scale: number;
  containerRef: React.RefObject<HTMLDivElement | null>;
  handleMouseDown: (e: React.MouseEvent) => void;
  handleMouseMove: (e: React.MouseEvent) => void;
  handleMouseUp: () => void;
  className?: string;
}

const PdfViewerContent = ({
  fileUrl,
  isPreviewDataLoading = false,
  scale,
  containerRef,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  className = '',
}: PdfViewerContentProps) => {
  const pdfDocRef = useRef<any>(null);
  const [pdfLoaded, setPdfLoaded] = useState<boolean>(false);
  const [pdfLib, setPdfLib] = useState<typeof pdfjsLib | null>(null);
  const renderTaskRef = useRef<any>(null);
  const [numPages, setNumPages] = useState<number>(0);
  const [renderedPages, setRenderedPages] = useState<HTMLCanvasElement[]>([]);

  useEffect(() => {
    const loadPdfLib = async () => {
      try {
        const pdfjsLib = await import('pdfjs-dist');
        pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.mjs`;
        setPdfLib(pdfjsLib);
      } catch (error) {
        console.error('Error loading PDF.js:', error);
        toast.error('Failed to load PDF');
      }
    };

    void loadPdfLib();
  }, []);

  const cancelRenderTask = () => {
    if (renderTaskRef.current && renderTaskRef.current.cancel) {
      renderTaskRef.current.cancel();
      renderTaskRef.current = null;
    }
  };

  const renderAllPages = async (currentScale: number) => {
    if (!pdfDocRef.current) return;

    const pages = [];
    for (let i = 1; i <= numPages; i++) {
      const page = await pdfDocRef.current.getPage(i);
      const viewport = page.getViewport({ scale: currentScale });

      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      await page.render({
        canvasContext: context,
        viewport: viewport,
      }).promise;

      pages.push(canvas);
    }
    setRenderedPages(pages);
  };

  useEffect(() => {
    cancelRenderTask();

    if (pdfDocRef.current) {
      pdfDocRef.current.destroy();
      pdfDocRef.current = null;
      setPdfLoaded(false);
    }

    if (fileUrl && pdfLib) {
      const loadingTask = pdfLib.getDocument(fileUrl);

      loadingTask.promise.then(
        (doc: any) => {
          pdfDocRef.current = doc;
          setNumPages(doc.numPages);
          setPdfLoaded(true);
          void renderAllPages(scale);
        },
        (err: any) => {
          console.error('Error loading PDF: ', err);
          toast.error('Failed to load PDF preview');
        }
      );
    }

    return () => {
      cancelRenderTask();
      if (pdfDocRef.current) {
        pdfDocRef.current.destroy();
        pdfDocRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileUrl, pdfLib]);

  useEffect(() => {
    if (pdfLoaded) {
      cancelRenderTask();
      void renderAllPages(scale);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scale, pdfLoaded]);

  return (
    <>
      {isPreviewDataLoading || !pdfLoaded ? (
        <div className='bg-custom-white relative animate-pulse rounded-2xl p-6'>
          <div className='mx-auto h-[calc(100vh-335px)] max-w-[600px] animate-pulse rounded-2xl bg-white' />
        </div>
      ) : pdfLoaded ? (
        <div
          className={`size-full cursor-grab overflow-auto select-none [&_*]:select-none [&_img]:pointer-events-none ${className}`}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          ref={containerRef}
        >
          <div className='flex flex-col items-center gap-8 p-8'>
            {renderedPages.map((canvas, index) => (
              <img
                key={`${index + 1}`}
                src={canvas.toDataURL()}
                alt={`Page ${index + 1}`}
                className='m-auto block max-w-none'
              />
            ))}
          </div>
        </div>
      ) : (
        <div className='flex h-full flex-col items-center justify-center p-4'>
          <p className='text-center text-sm text-gray-500'>
            {fileUrl && !isPreviewDataLoading ? (
              <Fragment>
                Unable to display PDF.{' '}
                <a
                  href={fileUrl}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-blue-500 hover:underline'
                >
                  Click here
                </a>{' '}
                to open it in a new tab.
              </Fragment>
            ) : (
              'No PDF to preview'
            )}
          </p>
        </div>
      )}
    </>
  );
};

export default PdfViewerContent;
