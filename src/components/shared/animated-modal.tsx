'use client';

import useLockBodyScroll from '@/hooks/use-lock-body-scroll';
import { cn } from '@/libs/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { X } from 'lucide-react';

interface Props {
  triggerProp: React.ReactNode;
  children: React.ReactNode;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  className?: string;
  onClose?: () => void;
}

const AnimatedModal = ({ triggerProp, children, className, isOpen, setIsOpen, onClose }: Props) => {
  const handleClose = () => {
    setIsOpen(false);
    onClose?.();
  };

  useLockBodyScroll(isOpen);

  return (
    <div>
      <div className='group relative cursor-pointer' onClick={() => setIsOpen(true)}>
        {triggerProp}
      </div>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className='fixed inset-0 !z-50 grid cursor-pointer place-items-center overflow-y-scroll bg-slate-900/20 p-8 backdrop-blur'
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              onClick={(e) => e.stopPropagation()}
              className={cn(
                'relative w-full max-w-lg cursor-default overflow-hidden rounded-lg bg-white p-6 text-gray-500 shadow-xl',
                className
              )}
            >
              <div className='absolute top-3 right-3 z-[99]'>
                <button
                  className='ring-offset-background focus:ring-ring z-[60] cursor-pointer rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none'
                  onClick={handleClose}
                >
                  <X className='size-5' />
                  <span className='sr-only'>Close</span>
                </button>
              </div>
              {children}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
export default AnimatedModal;
