'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { AlertCircle, Loader2 } from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'sonner';

/* eslint-disable @typescript-eslint/no-explicit-any */

interface DeleteConfirmationModalProps {
  children?: React.ReactNode;
  title?: string;
  description?: string;
  onConfirm: () => Promise<any>;
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ConfirmationModal({
  title = 'Delete Confirmation',
  description = 'Are you sure you want to delete this item? This action cannot be undone.',
  onConfirm,
  children,
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange,
}: DeleteConfirmationModalProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : internalOpen;
  const setOpen = isControlled ? controlledOnOpenChange || (() => {}) : setInternalOpen;

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onConfirm()
        .then()
        .catch((err: any) => console.log('Failed to delete item: ', JSON.stringify(err)));
      setOpen(false);
    } catch {
      toast.error('Failed to delete item');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <div className='flex items-center gap-4'>
            <div className='flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20'>
              <AlertCircle className='h-6 w-6 text-red-600 dark:text-red-400' />
            </div>
            <DialogTitle>{title}</DialogTitle>
          </div>
          <DialogDescription className='text-gray-dark max-w-sm pt-4 pb-2 text-center'>
            {description}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className='mt-2 grid grid-cols-2 gap-2'>
          <Button
            variant='outline'
            onClick={() => setOpen(false)}
            className='h-10 w-full sm:w-auto'
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant='destructive'
            onClick={handleDelete}
            className='group relative h-10 w-full overflow-hidden bg-rose-600 sm:w-auto'
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <span className='opacity-0'>Delete</span>
                <span className='absolute inset-0 flex items-center justify-center'>
                  <Loader2 className='h-4 w-4 animate-spin' />
                </span>
              </>
            ) : (
              <>
                <span className='relative z-10 flex items-center gap-1'>Confirm</span>
                <span className='absolute inset-0 translate-y-full bg-red-700'></span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
