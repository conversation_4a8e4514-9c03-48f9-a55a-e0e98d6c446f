import { Button } from './button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu';
import { cn } from '@/libs/utils';
import { MoreHorizontal } from 'lucide-react';
import React from 'react';

interface DropdownMenuItemType {
  icon?: React.ReactNode;
  label: string;
  labelClassName?: string;
  onClick?: () => void;
  disabled?: boolean;
}

interface SimpleDropdownProps {
  items: DropdownMenuItemType[];
  align?: 'start' | 'center' | 'end';
  triggerClassName?: string;
}

export const SimpleDropdown: React.FC<SimpleDropdownProps> = ({
  items,
  triggerClassName,
  align = 'end',
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className={cn(
            'h-8 w-8 p-0 focus-visible:ring-0 focus-visible:outline-none',
            triggerClassName
          )}
        >
          <MoreHorizontal className='text-gray-dark h-4 w-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align} className='text-gray-dark'>
        {items.map((item, index) => (
          <DropdownMenuItem key={index} onClick={item.onClick} disabled={item.disabled}>
            {item.icon && item.icon}
            <p className={item.labelClassName}>{item.label}</p>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
