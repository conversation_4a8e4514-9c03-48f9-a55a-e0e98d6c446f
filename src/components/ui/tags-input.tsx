import { cn } from '@/libs/utils';
import React, { useEffect, useState } from 'react';

interface TagsInputProps {
  initialTags?: string[];
  onChange?: (tags: string[]) => void;
}

export const TagsInput: React.FC<TagsInputProps> = ({ initialTags = [], onChange }) => {
  const [tags, setTags] = useState<string[]>(initialTags);
  const [inputValue, setInputValue] = useState<string>('');
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    if (initialTags && initialTags.length > 0) {
      setTags(initialTags);
    }
  }, [initialTags]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      if (!tags.includes(inputValue)) {
        const newTags = [...tags, inputValue.trim()];
        setTags(newTags);
        setInputValue('');
        onChange?.(newTags);
      }
    } else if (e.key === 'Backspace' && !inputValue) {
      const newTags = tags.slice(0, -1);
      setTags(newTags);
    }
  };

  const removeTag = (indexToRemove: number) => {
    const newTags = tags.filter((_, index) => index !== indexToRemove);
    setTags(newTags);
    onChange?.(newTags);
  };

  return (
    <div
      className={cn(
        'group border-input flex min-h-[48px] flex-wrap items-center gap-2 rounded-md border px-3 py-1.5',
        {
          'ring-ring ring-2 ring-offset-1 outline-none': isFocused,
        }
      )}
    >
      {tags.map((tag, index) => (
        <div
          key={index}
          className='bg-primary-50 text-primary-500 flex h-7 items-center rounded-2xl px-3 text-sm font-medium'
        >
          <span>{tag}</span>
          <button type='button' onClick={() => removeTag(index)} className='ml-2 text-sm font-bold'>
            &times;
          </button>
        </div>
      ))}

      <input
        type='text'
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder='Type and press Enter...'
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className='placeholder:text-muted-foreground w-[20px] grow border-none p-1 text-sm outline-none'
      />
    </div>
  );
};
