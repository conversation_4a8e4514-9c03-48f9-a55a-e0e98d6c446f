import { ConfirmationModal } from '../shared/confirmation-modal';
import { Button } from '../ui';
import { LogoWithText } from '@/assets';
import { timerService } from '@/features/interview/candidate-interview/services/timerService';
import { useCandidateStore } from '@/stores/candidate-store';
import { useRouter } from '@tanstack/react-router';
import { X } from 'lucide-react';
import { ReactNode } from 'react';

interface InterviewLayoutProps {
  children: ReactNode;
  isInterviewCompleted?: boolean;
}

const InterviewLayout = ({ children, isInterviewCompleted = false }: InterviewLayoutProps) => {
  const router = useRouter();
  const currentPath = router.state.location.pathname;
  const { jobTitle } = useCandidateStore();

  const handleExitInterview = async () => {
    timerService.completeInterview('manual');
  };

  return (
    <>
      <div className='flex items-center justify-between px-6 py-5'>
        <div className='flex items-center'>
          <LogoWithText
            height='26'
            width='95'
            onClick={() => {
              if (currentPath === '/interview') return;

              router.navigate({ to: '/' });
            }}
          />
        </div>
        <h1 className='text-lg font-semibold text-gray-900'>
          Interview for {jobTitle ?? 'Demo Job'}
        </h1>
        {currentPath === '/interview' && !isInterviewCompleted && (
          <ConfirmationModal
            title='Exit Interview'
            description='Are you sure you want to exit the interview? This action cannot be undone.'
            onConfirm={handleExitInterview}
          >
            <Button size={'lg'} variant={'destructive'}>
              <X className='text-white' />
              <span className='text-neutral-6000 text-sm'>Exit Interview</span>
            </Button>
          </ConfirmationModal>
        )}
      </div>
      {children}
    </>
  );
};
export default InterviewLayout;
