'use client';

import { cn } from '@/libs/utils';
import { useNavigate } from '@tanstack/react-router';
import { useEffect, useState } from 'react';

export function DemoHeader() {
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 50;
      setIsScrolled(scrolled);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav
      className={cn(
        'fixed top-0 right-0 left-0 z-10 border-b border-b-[#A496FF33] px-8 py-5 transition-all duration-300 ease-in-out lg:px-0',
        isScrolled ? 'border-b border-white/20 bg-white/50 shadow-xs backdrop-blur-md' : 'bg-white'
      )}
    >
      <div className='mx-auto flex max-w-300 items-center gap-3.5'>
        <img
          src='/images/vivasoft-logo.png'
          alt='Vivasoft Logo'
          className='h-8 w-19 cursor-pointer border-r-2 border-r-[#8C8C8C80]'
          onClick={() => navigate({ to: '/job-list' })}
        />

        <img
          src='/images/previa-logo-with-text.png'
          alt='Previa Logo'
          className='h-9 w-auto cursor-pointer'
          onClick={() => navigate({ to: '/job-list' })}
        />
      </div>
    </nav>
  );
}
