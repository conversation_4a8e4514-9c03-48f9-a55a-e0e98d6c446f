import { cn } from '@/libs/utils';
import { AnimatePresence, motion } from 'framer-motion';
import * as React from 'react';

interface Props {
  items: TabItem[];
  defaultTabIndex?: number;
  className?: string;
  onChange?: (index: number) => void;
  contentHeight?: string;
  stickyHeader?: boolean;
}

export function DataTabs({
  items,
  defaultTabIndex = 0,
  className,
  onChange,
  contentHeight,
  stickyHeader = false,
}: Props) {
  const [selected, setSelected] = React.useState<number>(defaultTabIndex);
  const [direction, setDirection] = React.useState(0);
  const [dimensions, setDimensions] = React.useState({ width: 0, left: 0 });

  const buttonRefs = React.useRef<Map<number, HTMLButtonElement>>(new Map());
  const containerRef = React.useRef<HTMLDivElement>(null);

  React.useLayoutEffect(() => {
    const updateDimensions = () => {
      const selectedButton = buttonRefs.current.get(selected);
      const container = containerRef.current;

      if (selectedButton && container) {
        const rect = selectedButton.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        setDimensions({
          width: rect.width,
          left: rect.left - containerRect.left,
        });
      }
    };

    requestAnimationFrame(() => {
      updateDimensions();
    });

    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, [selected]);

  const handleTabClick = (index: number) => {
    setDirection(index > selected ? 1 : -1);
    setSelected(index);
    onChange?.(index);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>, index: number) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleTabClick(index);
    }
  };

  const selectedItem = items[selected];

  const useDynamicHeight = !contentHeight;

  return (
    <div className={cn('flex flex-col gap-4', useDynamicHeight ? 'h-auto' : 'h-full')}>
      <div
        ref={containerRef}
        role='tablist'
        aria-label='tabs'
        className={cn(
          'flex items-center justify-between gap-1 py-1.5',
          'bg-custom-white mr-auto',
          'rounded-xl border',
          'transition-all duration-200',
          useDynamicHeight ? 'bg-custom-white z-50 backdrop-blur-sm' : 'relative mt-auto',
          stickyHeader ? 'sticky top-0 shadow' : '',
          className
        )}
      >
        <motion.div
          className={'bg-primary absolute z-[1] rounded-lg'}
          initial={false}
          animate={{
            width: dimensions.width - 8,
            x: dimensions.left + 4,
            opacity: 1,
          }}
          transition={{
            type: 'spring',
            stiffness: 400,
            damping: 30,
          }}
          style={{ height: 'calc(100% - 8px)', top: '4px' }}
        />

        <div
          className='relative z-[2] w-full gap-1'
          style={{
            display: 'grid',
            gridTemplateColumns: `repeat(${items.length}, 1fr)`,
          }}
        >
          {items.map((item, index) => {
            const isSelected = selected === index;
            return (
              <motion.button
                key={item.value}
                ref={(el) => {
                  if (el) buttonRefs.current.set(index, el);
                  else buttonRefs.current.delete(index);
                }}
                type='button'
                role='tab'
                aria-selected={isSelected}
                aria-controls={`panel-${index}`}
                id={`tab-${index}`}
                tabIndex={isSelected ? 0 : -1}
                onClick={() => !item.disabled && handleTabClick(index)}
                onKeyDown={(e) => !item.disabled && handleKeyDown(e, index)}
                disabled={item.disabled}
                className={cn(
                  'relative flex items-center justify-center rounded-lg px-4 py-1.5',
                  'text-sm font-medium transition-all duration-300',
                  'focus-visible:ring-ring focus-visible:ring-2 focus-visible:outline-none',
                  'truncate',
                  isSelected
                    ? 'text-white'
                    : 'text-gray-dark hover:bg-muted/50 hover:text-foreground',
                  item.disabled && 'cursor-not-allowed opacity-50'
                )}
              >
                {item.icon && <item.icon className='h-4 w-4' />}
                <span className='truncate'>{item.label}</span>
                {item.count && (
                  <span className='bg-muted text-muted-foreground ml-1 rounded-full px-1.5 py-0.5 text-xs'>
                    {item.count}
                  </span>
                )}
              </motion.button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className={cn('relative', useDynamicHeight ? '' : 'flex-1')}>
        <div
          className={cn(
            'border-strock relative w-full rounded-2xl border',
            useDynamicHeight ? 'h-auto' : 'h-full',
            selectedItem?.className
          )}
          style={useDynamicHeight ? {} : { height: contentHeight ?? '400px' }}
        >
          <div
            className={cn(
              'overflow-hidden rounded-2xl',
              useDynamicHeight ? 'relative' : 'absolute inset-0'
            )}
          >
            <AnimatePresence initial={false} mode='popLayout' custom={direction}>
              <motion.div
                key={`card-${selected}`}
                className={cn(
                  'bg-card will-change-transform',
                  useDynamicHeight ? 'relative w-full' : 'absolute inset-0 h-full w-full'
                )}
                style={{
                  backfaceVisibility: 'hidden',
                  WebkitBackfaceVisibility: 'hidden',
                }}
              >
                {selectedItem?.content()}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}
